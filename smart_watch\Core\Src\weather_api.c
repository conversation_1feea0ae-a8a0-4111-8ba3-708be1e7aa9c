/**
  ******************************************************************************
  * @file    weather_api.c
  * @brief   天气API模块实现 - OpenWeatherMap API接口
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "weather_api.h"
#include "json_parser.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// API配置
static char g_api_key[64] = {0};
static char g_current_city[32] = WEATHER_DEFAULT_CITY;

// 缓存的天气数据
static weather_data_t g_cached_weather = {0};
static uint32_t g_last_update_time = 0;

// HTTP缓冲区
static char g_http_buffer[WEATHER_HTTP_BUFFER_SIZE];
static char g_json_buffer[WEATHER_JSON_BUFFER_SIZE];

/* Private function prototypes -----------------------------------------------*/
static int Weather_ConnectToServer(void);
static int Weather_SendHTTPRequest(const char* url);
static int Weather_ReceiveHTTPResponse(http_response_t* response);
static weather_status_t Weather_ParseWeatherStatus(const char* main_weather);
static int Weather_ValidateAPIKey(const char* api_key);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 天气API模块初始化
 * @retval 0: 成功, -1: 失败
 */
int Weather_Init(void)
{
    // 初始化缓存数据
    memset(&g_cached_weather, 0, sizeof(g_cached_weather));
    g_cached_weather.data_valid = 0;
    g_last_update_time = 0;
    
    // 设置默认城市
    strncpy(g_current_city, WEATHER_DEFAULT_CITY, sizeof(g_current_city) - 1);
    g_current_city[sizeof(g_current_city) - 1] = '\0';
    
    WEATHER_LOG("Weather API initialized");
    return 0;
}

/**
 * @brief 设置API密钥
 * @param api_key: OpenWeatherMap API密钥
 * @retval 0: 成功, -1: 失败
 */
int Weather_SetAPIKey(const char* api_key)
{
    if (api_key == NULL || strlen(api_key) == 0) {
        WEATHER_LOG("Invalid API key");
        return -1;
    }
    
    if (Weather_ValidateAPIKey(api_key) != 0) {
        WEATHER_LOG("API key format invalid");
        return -1;
    }
    
    strncpy(g_api_key, api_key, sizeof(g_api_key) - 1);
    g_api_key[sizeof(g_api_key) - 1] = '\0';
    
    WEATHER_LOG("API key set successfully");
    return 0;
}

/**
 * @brief 设置查询城市
 * @param city: 城市名称
 * @retval 0: 成功, -1: 失败
 */
int Weather_SetCity(const char* city)
{
    if (city == NULL || strlen(city) == 0) {
        return -1;
    }
    
    strncpy(g_current_city, city, sizeof(g_current_city) - 1);
    g_current_city[sizeof(g_current_city) - 1] = '\0';
    
    WEATHER_LOG("City set to: %s", g_current_city);
    return 0;
}

/**
 * @brief 获取天气数据
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_GetData(weather_data_t* weather)
{
    return Weather_GetDataByCity(g_current_city, weather);
}

/**
 * @brief 获取指定城市的天气数据
 * @param city: 城市名称
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_GetDataByCity(const char* city, weather_data_t* weather)
{
    if (city == NULL || weather == NULL) {
        return -1;
    }
    
    if (strlen(g_api_key) == 0) {
        WEATHER_LOG("API key not set");
        weather->last_error = 1; // API key error
        return -1;
    }
    
    // 构建请求URL
    char url[WEATHER_URL_BUFFER_SIZE];
    if (Weather_BuildURL(city, g_api_key, url, sizeof(url)) != 0) {
        WEATHER_LOG("Failed to build URL");
        weather->last_error = 2; // URL build error
        return -1;
    }
    
    // 发送HTTP请求
    http_response_t response = {0};
    if (Weather_HTTPGet(url, &response) != 0) {
        WEATHER_LOG("HTTP request failed");
        weather->last_error = 3; // HTTP request error
        return -1;
    }
    
    // 解析JSON响应
    if (Weather_ParseJSON(response.data, weather) != 0) {
        WEATHER_LOG("JSON parsing failed");
        weather->last_error = 4; // JSON parse error
        return -1;
    }
    
    // 更新缓存
    memcpy(&g_cached_weather, weather, sizeof(weather_data_t));
    g_last_update_time = HAL_GetTick();
    
    WEATHER_LOG("Weather data updated successfully");
    return 0;
}

/**
 * @brief 构建天气API请求URL
 * @param city: 城市名称
 * @param api_key: API密钥
 * @param url_buffer: URL缓冲区
 * @param buffer_size: 缓冲区大小
 * @retval 0: 成功, -1: 失败
 */
int Weather_BuildURL(const char* city, const char* api_key, char* url_buffer, size_t buffer_size)
{
    if (city == NULL || api_key == NULL || url_buffer == NULL || buffer_size == 0) {
        return -1;
    }
    
    int ret = snprintf(url_buffer, buffer_size,
                      "%s?q=%s&appid=%s&units=%s&lang=%s",
                      WEATHER_API_PATH, city, api_key, 
                      WEATHER_DEFAULT_UNITS, WEATHER_DEFAULT_LANG);
    
    if (ret < 0 || ret >= buffer_size) {
        return -1; // Buffer overflow
    }
    
    return 0;
}

/**
 * @brief 天气状态转换为字符串
 * @param status: 天气状态
 * @retval 天气状态字符串
 */
const char* Weather_StatusToString(weather_status_t status)
{
    switch (status) {
        case WEATHER_STATUS_CLEAR:        return "晴天";
        case WEATHER_STATUS_CLOUDS:       return "多云";
        case WEATHER_STATUS_RAIN:         return "雨天";
        case WEATHER_STATUS_SNOW:         return "雪天";
        case WEATHER_STATUS_MIST:         return "雾天";
        case WEATHER_STATUS_THUNDERSTORM: return "雷暴";
        default:                          return "未知";
    }
}

/**
 * @brief 天气状态转换为图标字符
 * @param status: 天气状态
 * @retval 天气图标字符
 */
char Weather_StatusToIcon(weather_status_t status)
{
    switch (status) {
        case WEATHER_STATUS_CLEAR:        return 'O'; // 太阳
        case WEATHER_STATUS_CLOUDS:       return 'C'; // 云朵
        case WEATHER_STATUS_RAIN:         return 'R'; // 雨滴
        case WEATHER_STATUS_SNOW:         return 'S'; // 雪花
        case WEATHER_STATUS_MIST:         return 'M'; // 雾气
        case WEATHER_STATUS_THUNDERSTORM: return 'T'; // 雷电
        default:                          return '?'; // 未知
    }
}

/**
 * @brief 检查是否需要更新天气数据
 * @param last_update: 上次更新时间戳
 * @retval 1: 需要更新, 0: 不需要更新
 */
int Weather_NeedUpdate(uint32_t last_update)
{
    uint32_t current_time = HAL_GetTick();
    uint32_t elapsed = current_time - last_update;
    
    return (elapsed >= WEATHER_UPDATE_INTERVAL) ? 1 : 0;
}

/**
 * @brief 获取缓存的天气数据
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 无缓存数据
 */
int Weather_GetCachedData(weather_data_t* weather)
{
    if (weather == NULL) {
        return -1;
    }

    if (g_cached_weather.data_valid == 0) {
        return -1; // 无有效缓存数据
    }

    memcpy(weather, &g_cached_weather, sizeof(weather_data_t));
    return 0;
}

/**
 * @brief 发送HTTP GET请求
 * @param url: 请求URL
 * @param response: 响应结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_HTTPGet(const char* url, http_response_t* response)
{
    if (url == NULL || response == NULL) {
        return -1;
    }

    // 连接到服务器
    if (Weather_ConnectToServer() != 0) {
        WEATHER_LOG("Failed to connect to server");
        return -1;
    }

    // 发送HTTP请求
    if (Weather_SendHTTPRequest(url) != 0) {
        WEATHER_LOG("Failed to send HTTP request");
        return -1;
    }

    // 接收HTTP响应
    if (Weather_ReceiveHTTPResponse(response) != 0) {
        WEATHER_LOG("Failed to receive HTTP response");
        return -1;
    }

    return 0;
}

/**
 * @brief 解析天气JSON数据
 * @param json_data: JSON字符串
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_ParseJSON(const char* json_data, weather_data_t* weather)
{
    if (json_data == NULL || weather == NULL) {
        return -1;
    }

    // 初始化天气数据
    memset(weather, 0, sizeof(weather_data_t));

    // 解析主要天气信息
    if (JSON_GetNestedFloat(json_data, "main", "temp", &weather->temperature) != JSON_PARSE_OK) {
        WEATHER_LOG("Failed to parse temperature");
        return -1;
    }

    if (JSON_GetNestedFloat(json_data, "main", "humidity", &weather->humidity) != JSON_PARSE_OK) {
        WEATHER_LOG("Failed to parse humidity");
        return -1;
    }

    if (JSON_GetNestedFloat(json_data, "main", "pressure", &weather->pressure) != JSON_PARSE_OK) {
        WEATHER_LOG("Failed to parse pressure");
        return -1;
    }

    // 解析天气状态
    char main_weather[32];
    if (JSON_GetArrayFirstString(json_data, "weather", "main", main_weather, sizeof(main_weather)) == JSON_PARSE_OK) {
        weather->status = Weather_ParseWeatherStatus(main_weather);
    }

    // 解析天气描述
    JSON_GetArrayFirstString(json_data, "weather", "description", weather->description, sizeof(weather->description));

    // 解析城市名称
    JSON_GetString(json_data, "name", weather->city_name, sizeof(weather->city_name));

    // 解析国家代码
    JSON_GetNestedString(json_data, "sys", "country", weather->country, sizeof(weather->country));

    // 设置更新时间
    weather->update_time = HAL_GetTick();
    weather->data_valid = 1;
    weather->last_error = 0;

    WEATHER_LOG("JSON parsing completed successfully");
    return 0;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 连接到天气API服务器
 * @retval 0: 成功, -1: 失败
 */
static int Weather_ConnectToServer(void)
{
    char command[128];
    snprintf(command, sizeof(command), "AT+CIPSTART=\"TCP\",\"%s\",%d",
             WEATHER_API_HOST, WEATHER_API_PORT);

    at_response_t result = WiFi_SendATCommand(command, WEATHER_CONNECT_TIMEOUT);
    if (result == AT_RESPONSE_OK) {
        WEATHER_LOG("Connected to weather server");
        return 0;
    }

    WEATHER_LOG("Failed to connect to weather server");
    return -1;
}

/**
 * @brief 发送HTTP请求
 * @param url: 请求URL路径
 * @retval 0: 成功, -1: 失败
 */
static int Weather_SendHTTPRequest(const char* url)
{
    // 构建HTTP GET请求
    int content_length = snprintf(g_http_buffer, sizeof(g_http_buffer),
                                 "GET %s HTTP/1.1\r\n"
                                 "Host: %s\r\n"
                                 "Connection: close\r\n"
                                 "User-Agent: SmartWatch/1.0\r\n"
                                 "\r\n",
                                 url, WEATHER_API_HOST);

    if (content_length < 0 || content_length >= sizeof(g_http_buffer)) {
        return -1;
    }

    // 发送数据长度
    char send_cmd[32];
    snprintf(send_cmd, sizeof(send_cmd), "AT+CIPSEND=%d", content_length);

    at_response_t result = WiFi_SendATCommand(send_cmd, AT_TIMEOUT_MEDIUM);
    if (result != AT_RESPONSE_OK) {
        return -1;
    }

    // 发送HTTP请求数据
    HAL_UART_Transmit(&huart1, (uint8_t*)g_http_buffer, content_length, 5000);

    WEATHER_LOG("HTTP request sent");
    return 0;
}

/**
 * @brief 接收HTTP响应
 * @param response: 响应结构体指针
 * @retval 0: 成功, -1: 失败
 */
static int Weather_ReceiveHTTPResponse(http_response_t* response)
{
    // 等待接收数据
    uint32_t start_time = HAL_GetTick();
    uint32_t timeout = WEATHER_HTTP_TIMEOUT;

    memset(g_json_buffer, 0, sizeof(g_json_buffer));
    uint16_t received_length = 0;

    while ((HAL_GetTick() - start_time) < timeout) {
        // 这里应该从WiFi模块接收数据
        // 简化实现：假设数据已经在缓冲区中
        if (received_length > 0) {
            break;
        }
        osDelay(100);
    }

    if (received_length == 0) {
        return -1;
    }

    // 查找HTTP响应体（JSON数据）
    char* json_start = strstr(g_json_buffer, "\r\n\r\n");
    if (json_start != NULL) {
        json_start += 4; // 跳过 "\r\n\r\n"
        response->data = json_start;
        response->size = strlen(json_start);
        response->status_code = 200; // 简化处理

        WEATHER_LOG("HTTP response received");
        return 0;
    }

    return -1;
}

/**
 * @brief 解析天气状态字符串
 * @param main_weather: 主要天气字符串
 * @retval 天气状态枚举
 */
static weather_status_t Weather_ParseWeatherStatus(const char* main_weather)
{
    if (main_weather == NULL) {
        return WEATHER_STATUS_UNKNOWN;
    }

    if (strcmp(main_weather, "Clear") == 0) {
        return WEATHER_STATUS_CLEAR;
    } else if (strcmp(main_weather, "Clouds") == 0) {
        return WEATHER_STATUS_CLOUDS;
    } else if (strcmp(main_weather, "Rain") == 0) {
        return WEATHER_STATUS_RAIN;
    } else if (strcmp(main_weather, "Snow") == 0) {
        return WEATHER_STATUS_SNOW;
    } else if (strcmp(main_weather, "Mist") == 0 || strcmp(main_weather, "Fog") == 0) {
        return WEATHER_STATUS_MIST;
    } else if (strcmp(main_weather, "Thunderstorm") == 0) {
        return WEATHER_STATUS_THUNDERSTORM;
    }

    return WEATHER_STATUS_UNKNOWN;
}

/**
 * @brief 验证API密钥格式
 * @param api_key: API密钥
 * @retval 0: 有效, -1: 无效
 */
static int Weather_ValidateAPIKey(const char* api_key)
{
    if (api_key == NULL) {
        return -1;
    }

    size_t len = strlen(api_key);

    // OpenWeatherMap API密钥通常是32位十六进制字符串
    if (len != 32) {
        return -1;
    }

    // 检查是否都是十六进制字符
    for (size_t i = 0; i < len; i++) {
        char c = api_key[i];
        if (!((c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'))) {
            return -1;
        }
    }

    return 0;
}
