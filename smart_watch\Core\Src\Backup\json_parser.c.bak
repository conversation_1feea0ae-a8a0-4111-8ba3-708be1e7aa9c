/**
  ******************************************************************************
  * @file    json_parser.c
  * @brief   轻量级JSON解析器实现 - 专用于天气数据解析
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "json_parser.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static const char* JSON_FindKey(const char* json, const char* key);
static const char* JSON_FindValueStart(const char* key_pos);
static const char* JSON_FindValueEnd(const char* value_start);
static int JSON_IsWhitespace(char c);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 从JSON字符串中提取字符串值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetString(const char* json, const char* key, char* value, size_t value_size)
{
    if (json == NULL || key == NULL || value == NULL || value_size == 0) {
        return JSON_PARSE_ERROR;
    }
    
    // 查找键
    const char* key_pos = JSON_FindKey(json, key);
    if (key_pos == NULL) {
        return JSON_PARSE_NOT_FOUND;
    }
    
    // 查找值的开始位置
    const char* value_start = JSON_FindValueStart(key_pos);
    if (value_start == NULL) {
        return JSON_PARSE_INVALID_FORMAT;
    }
    
    // 查找值的结束位置
    const char* value_end = JSON_FindValueEnd(value_start);
    if (value_end == NULL) {
        return JSON_PARSE_INVALID_FORMAT;
    }
    
    // 提取值（去除引号）
    if (*value_start == '"') {
        value_start++; // 跳过开始引号
        if (*(value_end - 1) == '"') {
            value_end--; // 跳过结束引号
        }
    }
    
    size_t len = value_end - value_start;
    if (len >= value_size) {
        len = value_size - 1;
    }
    
    strncpy(value, value_start, len);
    value[len] = '\0';
    
    return JSON_PARSE_OK;
}

/**
 * @brief 从JSON字符串中提取浮点数值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出浮点数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetFloat(const char* json, const char* key, float* value)
{
    if (json == NULL || key == NULL || value == NULL) {
        return JSON_PARSE_ERROR;
    }
    
    char str_value[JSON_MAX_NUMBER_LEN];
    json_parse_result_t result = JSON_GetString(json, key, str_value, sizeof(str_value));
    
    if (result == JSON_PARSE_OK) {
        *value = atof(str_value);
    }
    
    return result;
}

/**
 * @brief 从JSON字符串中提取整数值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出整数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetInt(const char* json, const char* key, int* value)
{
    if (json == NULL || key == NULL || value == NULL) {
        return JSON_PARSE_ERROR;
    }
    
    char str_value[JSON_MAX_NUMBER_LEN];
    json_parse_result_t result = JSON_GetString(json, key, str_value, sizeof(str_value));
    
    if (result == JSON_PARSE_OK) {
        *value = atoi(str_value);
    }
    
    return result;
}

/**
 * @brief 从嵌套JSON对象中提取浮点数值
 * @param json: JSON字符串
 * @param parent_key: 父对象键名
 * @param child_key: 子对象键名
 * @param value: 输出浮点数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetNestedFloat(const char* json, const char* parent_key, 
                                       const char* child_key, float* value)
{
    if (json == NULL || parent_key == NULL || child_key == NULL || value == NULL) {
        return JSON_PARSE_ERROR;
    }
    
    // 查找父对象
    const char* parent_obj = JSON_FindObject(json, parent_key);
    if (parent_obj == NULL) {
        return JSON_PARSE_NOT_FOUND;
    }
    
    // 在父对象中查找子键值
    return JSON_GetFloat(parent_obj, child_key, value);
}

/**
 * @brief 从嵌套JSON对象中提取字符串值
 * @param json: JSON字符串
 * @param parent_key: 父对象键名
 * @param child_key: 子对象键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetNestedString(const char* json, const char* parent_key, 
                                        const char* child_key, char* value, size_t value_size)
{
    if (json == NULL || parent_key == NULL || child_key == NULL || value == NULL) {
        return JSON_PARSE_ERROR;
    }
    
    // 查找父对象
    const char* parent_obj = JSON_FindObject(json, parent_key);
    if (parent_obj == NULL) {
        return JSON_PARSE_NOT_FOUND;
    }
    
    // 在父对象中查找子键值
    return JSON_GetString(parent_obj, child_key, value, value_size);
}

/**
 * @brief 从JSON数组中提取第一个对象的字符串值
 * @param json: JSON字符串
 * @param array_key: 数组键名
 * @param object_key: 对象键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetArrayFirstString(const char* json, const char* array_key, 
                                            const char* object_key, char* value, size_t value_size)
{
    if (json == NULL || array_key == NULL || object_key == NULL || value == NULL) {
        return JSON_PARSE_ERROR;
    }
    
    // 查找数组
    const char* array_start = JSON_FindArray(json, array_key);
    if (array_start == NULL) {
        return JSON_PARSE_NOT_FOUND;
    }
    
    // 跳过 '['
    array_start++;
    while (JSON_IsWhitespace(*array_start)) array_start++;
    
    // 检查是否为空数组
    if (*array_start == ']') {
        return JSON_PARSE_NOT_FOUND;
    }
    
    // 在第一个对象中查找键值
    return JSON_GetString(array_start, object_key, value, value_size);
}

/**
 * @brief 查找JSON对象的开始位置
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @retval 对象开始位置指针，NULL表示未找到
 */
const char* JSON_FindObject(const char* json, const char* key)
{
    const char* key_pos = JSON_FindKey(json, key);
    if (key_pos == NULL) {
        return NULL;
    }
    
    const char* value_start = JSON_FindValueStart(key_pos);
    if (value_start == NULL || *value_start != '{') {
        return NULL;
    }
    
    return value_start;
}

/**
 * @brief 查找JSON数组的开始位置
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @retval 数组开始位置指针，NULL表示未找到
 */
const char* JSON_FindArray(const char* json, const char* key)
{
    const char* key_pos = JSON_FindKey(json, key);
    if (key_pos == NULL) {
        return NULL;
    }
    
    const char* value_start = JSON_FindValueStart(key_pos);
    if (value_start == NULL || *value_start != '[') {
        return NULL;
    }
    
    return value_start;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 查找JSON键的位置
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @retval 键位置指针，NULL表示未找到
 */
static const char* JSON_FindKey(const char* json, const char* key)
{
    if (json == NULL || key == NULL) {
        return NULL;
    }
    
    char search_pattern[JSON_MAX_STRING_LEN];
    snprintf(search_pattern, sizeof(search_pattern), "\"%s\"", key);
    
    const char* pos = strstr(json, search_pattern);
    return pos;
}

/**
 * @brief 查找值的开始位置（跳过冒号和空白字符）
 * @param key_pos: 键的位置
 * @retval 值开始位置指针，NULL表示未找到
 */
static const char* JSON_FindValueStart(const char* key_pos)
{
    if (key_pos == NULL) {
        return NULL;
    }
    
    // 跳过键名，查找冒号
    const char* colon_pos = strchr(key_pos, ':');
    if (colon_pos == NULL) {
        return NULL;
    }
    
    // 跳过冒号和空白字符
    colon_pos++;
    while (JSON_IsWhitespace(*colon_pos)) {
        colon_pos++;
    }
    
    return colon_pos;
}

/**
 * @brief 查找值的结束位置
 * @param value_start: 值开始位置
 * @retval 值结束位置指针，NULL表示未找到
 */
static const char* JSON_FindValueEnd(const char* value_start)
{
    if (value_start == NULL) {
        return NULL;
    }
    
    const char* pos = value_start;
    
    if (*pos == '"') {
        // 字符串值：查找结束引号
        pos++; // 跳过开始引号
        while (*pos && *pos != '"') {
            if (*pos == '\\') {
                pos++; // 跳过转义字符
            }
            pos++;
        }
        if (*pos == '"') {
            pos++; // 包含结束引号
        }
    } else if (*pos == '{') {
        // 对象值：查找匹配的右大括号
        int brace_count = 1;
        pos++;
        while (*pos && brace_count > 0) {
            if (*pos == '{') brace_count++;
            else if (*pos == '}') brace_count--;
            pos++;
        }
    } else if (*pos == '[') {
        // 数组值：查找匹配的右方括号
        int bracket_count = 1;
        pos++;
        while (*pos && bracket_count > 0) {
            if (*pos == '[') bracket_count++;
            else if (*pos == ']') bracket_count--;
            pos++;
        }
    } else {
        // 数字或布尔值：查找分隔符
        while (*pos && *pos != ',' && *pos != '}' && *pos != ']' && !JSON_IsWhitespace(*pos)) {
            pos++;
        }
    }
    
    return pos;
}

/**
 * @brief 检查字符是否为空白字符
 * @param c: 要检查的字符
 * @retval 1: 是空白字符, 0: 不是空白字符
 */
static int JSON_IsWhitespace(char c)
{
    return (c == ' ' || c == '\t' || c == '\n' || c == '\r');
}
