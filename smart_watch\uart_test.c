/**
 * @file uart_test.c
 * @brief UART通信测试工具
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#include "main.h"
#include "usart.h"
#include "cmsis_os.h"
#include <string.h>
#include <stdio.h>

/* 测试缓冲区 */
static char test_tx_buffer[256];
static char test_rx_buffer[256];
static volatile int test_rx_index = 0;
static volatile int test_complete = 0;

/**
 * @brief 清空测试接收缓冲区
 */
static void Test_ClearRxBuffer(void)
{
    memset(test_rx_buffer, 0, sizeof(test_rx_buffer));
    test_rx_index = 0;
    test_complete = 0;
}

/**
 * @brief 发送测试数据
 * @param data: 要发送的数据
 * @param len: 数据长度
 */
static void Test_SendData(const char* data, int len)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)data, len, 1000);
}

/**
 * @brief 等待接收完成
 * @param timeout_ms: 超时时间(毫秒)
 * @retval 0: 成功, -1: 超时
 */
static int Test_WaitForResponse(int timeout_ms)
{
    int elapsed = 0;
    while (elapsed < timeout_ms && !test_complete) {
        osDelay(10);
        elapsed += 10;
    }
    return test_complete ? 0 : -1;
}

/**
 * @brief 基本连通性测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Basic(void)
{
    static char result[128];
    int test_count = 0;
    int success_count = 0;
    
    // 测试1: 发送AT指令
    Test_ClearRxBuffer();
    Test_SendData("AT\r\n", 4);
    test_count++;
    if (Test_WaitForResponse(1000) == 0) {
        if (strstr(test_rx_buffer, "OK") != NULL) {
            success_count++;
        }
    }
    
    osDelay(500);
    
    // 测试2: 发送回显测试
    Test_ClearRxBuffer();
    Test_SendData("ATE1\r\n", 6);
    test_count++;
    if (Test_WaitForResponse(1000) == 0) {
        if (strstr(test_rx_buffer, "OK") != NULL) {
            success_count++;
        }
    }
    
    osDelay(500);
    
    // 测试3: 获取版本信息
    Test_ClearRxBuffer();
    Test_SendData("AT+GMR\r\n", 8);
    test_count++;
    if (Test_WaitForResponse(2000) == 0) {
        if (strlen(test_rx_buffer) > 10) { // 版本信息应该比较长
            success_count++;
        }
    }
    
    snprintf(result, sizeof(result), "Basic Test: %d/%d passed", success_count, test_count);
    return result;
}

/**
 * @brief 波特率测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Baudrate(void)
{
    static char result[128];
    uint32_t baudrates[] = {9600, 38400, 57600, 115200};
    int baudrate_count = sizeof(baudrates) / sizeof(baudrates[0]);
    int success_count = 0;
    
    for (int i = 0; i < baudrate_count; i++) {
        // 重新配置UART波特率
        huart1.Init.BaudRate = baudrates[i];
        if (HAL_UART_Init(&huart1) == HAL_OK) {
            osDelay(100);
            
            // 测试AT指令
            Test_ClearRxBuffer();
            Test_SendData("AT\r\n", 4);
            
            if (Test_WaitForResponse(1000) == 0) {
                if (strstr(test_rx_buffer, "OK") != NULL) {
                    success_count++;
                    snprintf(result, sizeof(result), "Working baudrate: %lu", baudrates[i]);
                    return result;
                }
            }
        }
        osDelay(500);
    }
    
    // 恢复默认波特率
    huart1.Init.BaudRate = 115200;
    HAL_UART_Init(&huart1);
    
    snprintf(result, sizeof(result), "No working baudrate found");
    return result;
}

/**
 * @brief 回环测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Loopback(void)
{
    static char result[128];
    const char* test_strings[] = {"HELLO", "12345", "TEST"};
    int test_count = sizeof(test_strings) / sizeof(test_strings[0]);
    int success_count = 0;
    
    for (int i = 0; i < test_count; i++) {
        Test_ClearRxBuffer();
        
        // 发送测试字符串
        snprintf(test_tx_buffer, sizeof(test_tx_buffer), "%s\r\n", test_strings[i]);
        Test_SendData(test_tx_buffer, strlen(test_tx_buffer));
        
        if (Test_WaitForResponse(1000) == 0) {
            if (strstr(test_rx_buffer, test_strings[i]) != NULL) {
                success_count++;
            }
        }
        osDelay(500);
    }
    
    snprintf(result, sizeof(result), "Loopback: %d/%d passed", success_count, test_count);
    return result;
}

/**
 * @brief 完整的UART诊断测试
 * @retval 诊断结果字符串
 */
const char* UART_Test_Complete(void)
{
    static char result[256];
    char temp[64];
    
    strcpy(result, "UART Diagnosis:\n");
    
    // 基本测试
    const char* basic_result = UART_Test_Basic();
    snprintf(temp, sizeof(temp), "1.%s\n", basic_result);
    strcat(result, temp);
    
    // 波特率测试
    const char* baud_result = UART_Test_Baudrate();
    snprintf(temp, sizeof(temp), "2.%s\n", baud_result);
    strcat(result, temp);
    
    // 回环测试
    const char* loop_result = UART_Test_Loopback();
    snprintf(temp, sizeof(temp), "3.%s", loop_result);
    strcat(result, temp);
    
    return result;
}

/**
 * @brief UART接收中断回调（测试用）
 * @param huart: UART句柄
 */
void Test_UART_RxCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        // 简单的接收处理
        if (test_rx_index < sizeof(test_rx_buffer) - 1) {
            // 检查是否接收到完整响应
            if (test_rx_buffer[test_rx_index-1] == '\n' || 
                strstr(test_rx_buffer, "OK") != NULL ||
                strstr(test_rx_buffer, "ERROR") != NULL) {
                test_complete = 1;
            }
        }
    }
}
