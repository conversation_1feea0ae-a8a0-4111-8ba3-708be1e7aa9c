# 按键中断功能测试指南

## 测试目的
验证按键中断实现的正确性和性能，确保所有按键功能正常工作。

## 测试环境准备

### 1. 硬件连接
- 确保所有按键正确连接到PB12-PB15
- 验证上拉电阻工作正常
- 检查按键按下时为低电平

### 2. 软件准备
- 编译并下载最新固件
- 连接调试器（ST-Link等）
- 打开串口调试工具（如果有串口输出）

## 测试项目

### 1. 基本中断响应测试

#### 测试步骤：
1. 按下任意按键
2. 观察是否有立即的音效反馈
3. 检查显示是否立即更新

#### 预期结果：
- 按键按下瞬间听到蜂鸣器响声
- 显示内容立即发生变化
- 响应时间 < 1ms

#### 调试监控：
```c
// 在调试器中观察这些变量
g_key_interrupt_flag     // 应该立即变为1
g_key_press_time        // 记录按键时间戳
g_debug_counter         // 每次按键递增
```

### 2. 单个按键功能测试

#### KEY_UP按键测试
```
测试场景1: 时钟模式下按KEY_UP
预期结果: 切换到传感器显示模式

测试场景2: 设置模式下按KEY_UP  
预期结果: 增加当前设置项数值

测试场景3: 计时器模式下按KEY_UP
预期结果: 启动/暂停计时器
```

#### KEY_DOWN按键测试
```
测试场景1: 传感器模式下按KEY_DOWN
预期结果: 返回时钟显示模式

测试场景2: 设置模式下按KEY_DOWN
预期结果: 减少当前设置项数值

测试场景3: 计时器模式下按KEY_DOWN
预期结果: 重置计时器
```

#### KEY_MENU按键测试
```
测试场景1: 正常模式下按KEY_MENU
预期结果: 循环切换显示模式（时钟→传感器→闹钟→计时器→秒表→时钟）

测试场景2: 设置模式下按KEY_MENU
预期结果: 保存设置并退出设置模式
```

#### KEY_SET按键测试
```
测试场景1: 短按KEY_SET（<2秒）
预期结果: 在设置模式中切换设置项

测试场景2: 长按KEY_SET（≥2秒）
预期结果: 进入设置模式
```

### 3. 防抖功能测试

#### 测试步骤：
1. 快速连续按同一按键（模拟抖动）
2. 观察是否只响应一次
3. 测试不同按键的防抖效果

#### 预期结果：
- 50ms内的重复触发被忽略
- 只有第一次按键生效
- 音效只播放一次

### 4. 长按功能测试

#### 测试步骤：
1. 长按KEY_SET按键2秒以上
2. 观察是否进入设置模式
3. 测试长按期间的状态变化

#### 预期结果：
- 2秒后自动进入设置模式
- 显示切换到设置界面
- 长按标志正确设置

### 5. 中断优先级测试

#### 测试步骤：
1. 在系统繁忙时按键（如传感器读取期间）
2. 观察按键响应是否受影响
3. 测试多个按键同时按下的情况

#### 预期结果：
- 按键响应不受其他任务影响
- 中断能够正常抢占低优先级任务
- 多按键同时按下都能正确识别

## 性能测试

### 1. 响应时间测试

#### 测试方法：
使用示波器或逻辑分析仪测量：
- 按键按下到GPIO中断触发的时间
- 中断触发到音效输出的时间
- 中断触发到显示更新的时间

#### 性能指标：
- 硬件响应时间: < 10μs
- 软件响应时间: < 1ms
- 总响应时间: < 1ms

### 2. CPU占用测试

#### 测试方法：
```c
// 在KeyTask中添加性能监控
static uint32_t task_start_time = 0;
static uint32_t total_execution_time = 0;
static uint32_t execution_count = 0;

// 任务开始
task_start_time = HAL_GetTick();

// 任务结束
total_execution_time += HAL_GetTick() - task_start_time;
execution_count++;

// 计算平均执行时间
uint32_t avg_time = total_execution_time / execution_count;
```

#### 性能指标：
- 平均任务执行时间: < 1ms
- CPU占用率: < 5%（相比轮询模式的50%+）

### 3. 功耗测试

#### 测试方法：
- 使用电流表测量系统待机电流
- 对比轮询模式和中断模式的功耗差异
- 测试按键活动期间的功耗变化

#### 预期改善：
- 待机电流降低15-25%
- 按键响应期间功耗峰值更低
- 整体功耗效率提升

## 故障排除

### 常见问题及解决方案

#### 1. 按键无响应
```
可能原因:
- NVIC中断未正确配置
- GPIO配置错误
- 中断服务程序未正确实现

检查方法:
- 验证HAL_NVIC_EnableIRQ()调用
- 检查GPIO_MODE_IT_FALLING配置
- 确认EXTI15_10_IRQHandler()存在
```

#### 2. 按键响应异常
```
可能原因:
- 防抖时间设置不当
- 中断标志未正确清除
- 按键状态检查逻辑错误

检查方法:
- 调整KEY_DEBOUNCE_TIME值
- 确认中断标志及时清零
- 验证GPIO_PIN_RESET判断
```

#### 3. 长按功能失效
```
可能原因:
- 长按时间阈值设置错误
- 长按标志逻辑错误
- 按键释放检测问题

检查方法:
- 确认KEY_LONG_PRESS_TIME值
- 检查long_press_triggered逻辑
- 验证按键释放检测代码
```

## 测试报告模板

### 测试结果记录
```
测试日期: ___________
测试人员: ___________
固件版本: ___________

基本功能测试:
□ KEY_UP响应正常
□ KEY_DOWN响应正常  
□ KEY_MENU响应正常
□ KEY_SET短按正常
□ KEY_SET长按正常

性能测试:
响应时间: _____ ms
CPU占用: _____ %
功耗改善: _____ %

问题记录:
1. ________________
2. ________________
3. ________________

总体评价:
□ 优秀  □ 良好  □ 一般  □ 需改进
```

## 验收标准

### 功能要求
- ✅ 所有按键响应正常
- ✅ 防抖功能有效
- ✅ 长按功能正确
- ✅ 音效反馈及时

### 性能要求
- ✅ 响应时间 < 1ms
- ✅ CPU占用 < 5%
- ✅ 功耗降低 > 15%
- ✅ 无误触发现象

### 稳定性要求
- ✅ 连续测试1小时无异常
- ✅ 快速按键无丢失
- ✅ 系统负载下响应正常
- ✅ 长时间运行稳定

通过以上测试，可以全面验证按键中断实现的正确性和性能优势。
