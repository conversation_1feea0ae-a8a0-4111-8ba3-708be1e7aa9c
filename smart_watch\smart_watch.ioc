#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
FREERTOS.BinarySemaphores01=TimeUpdateSem,Dynamic,NULL,Available
FREERTOS.FootprintOK=false
FREERTOS.IPParameters=Tasks01,FootprintOK,Queues01,Mutexes01,BinarySemaphores01,configTOTAL_HEAP_SIZE
FREERTOS.Mutexes01=DisplayMutex,Dynamic,NULL,Available
FREERTOS.Queues01=KeyEventQueue,5,uint8_t,0,Dynamic,NULL,NULL;SensorDataQueue,2,uint32_t,0,Dynamic,NULL,NULL
FREERTOS.Tasks01=defaultTask,24,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL;KeyTask,40,128,StartKeyTask,Default,NULL,Dynamic,NULL,NULL;SensorTask,16,128,StartSensorTask,Default,NULL,Dynamic,NULL,NULL;DisplayTask,32,128,StartD<PERSON>playTask,Default,NULL,Dynamic,NULL,NULL;WiFiTask,8,128,StartWiFiTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTOTAL_HEAP_SIZE=4096
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=FREERTOS
Mcu.IP1=I2C1
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=USART1
Mcu.IPNb=7
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC15-OSC32_OUT
Mcu.Pin1=PD0-OSC_IN
Mcu.Pin10=PB14
Mcu.Pin11=PB15
Mcu.Pin12=PA9
Mcu.Pin13=PA10
Mcu.Pin14=PA13
Mcu.Pin15=PA14
Mcu.Pin16=PB6
Mcu.Pin17=PB7
Mcu.Pin18=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin19=VP_SYS_VS_tim4
Mcu.Pin2=PD1-OSC_OUT
Mcu.Pin3=PA5
Mcu.Pin4=PA6
Mcu.Pin5=PA7
Mcu.Pin6=PB10
Mcu.Pin7=PB11
Mcu.Pin8=PB12
Mcu.Pin9=PB13
Mcu.PinsNb=20
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.14.0
MxDb.Version=DB.6.0.140
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.EXTI15_10_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM4_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM4_IRQn
NVIC.TimeBaseIP=TIM4
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=DS1302_CLK
PA5.Locked=true
PA5.Signal=GPIO_Output
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=DS1302_DAT
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=DS1302_RST
PA7.Locked=true
PA7.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB12.GPIO_Label=KEY_SET
PB12.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB12.GPIO_PuPd=GPIO_PULLUP
PB12.Locked=true
PB12.Signal=GPXTI12
PB13.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB13.GPIO_Label=KEY_UP
PB13.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB13.GPIO_PuPd=GPIO_PULLUP
PB13.Locked=true
PB13.Signal=GPXTI13
PB14.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB14.GPIO_Label=KEY_DOWN
PB14.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.Locked=true
PB14.Signal=GPXTI14
PB15.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB15.GPIO_Label=KEY_MENU
PB15.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB15.GPIO_PuPd=GPIO_PULLUP
PB15.Locked=true
PB15.Signal=GPXTI15
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC15-OSC32_OUT.GPIOParameters=PinState,GPIO_Label
PC15-OSC32_OUT.GPIO_Label=BUZZER_PIN
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.PinState=GPIO_PIN_SET
PC15-OSC32_OUT.Signal=GPIO_Output
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=true
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=smart_watch.ioc
ProjectManager.ProjectName=smart_watch
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_I2C1_Init-I2C1-false-HAL-true,4-MX_I2C2_Init-I2C2-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.GPXTI12.0=GPIO_EXTI12
SH.GPXTI12.ConfNb=1
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
SH.GPXTI14.0=GPIO_EXTI14
SH.GPXTI14.ConfNb=1
SH.GPXTI15.0=GPIO_EXTI15
SH.GPXTI15.ConfNb=1
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim4.Mode=TIM4
VP_SYS_VS_tim4.Signal=SYS_VS_tim4
board=custom
rtos.0.ip=FREERTOS
