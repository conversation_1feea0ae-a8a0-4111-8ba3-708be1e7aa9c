Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to cmsis_os2.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    aht10.o(i.AHT10_GetTemperatureHumidity) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_I2C_Scan) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    aht10.o(i.AHT10_I2C_Scan) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    aht10.o(i.AHT10_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    aht10.o(i.AHT10_Init) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_Init) refers to aht10.o(.data) for .data
    aht10.o(i.AHT10_IsBusy) refers to aht10.o(i.AHT10_ReadStatus) for AHT10_ReadStatus
    aht10.o(i.AHT10_IsCalibrated) refers to aht10.o(.data) for .data
    aht10.o(i.AHT10_IsPresent) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    aht10.o(i.AHT10_IsPresent) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_ReadData) refers to aht10.o(i.AHT10_ReadRawData) for AHT10_ReadRawData
    aht10.o(i.AHT10_ReadData) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    aht10.o(i.AHT10_ReadData) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    aht10.o(i.AHT10_ReadData) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    aht10.o(i.AHT10_ReadData) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    aht10.o(i.AHT10_ReadData) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    aht10.o(i.AHT10_ReadRawData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    aht10.o(i.AHT10_ReadRawData) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_ReadStatus) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    aht10.o(i.AHT10_ReadStatus) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_SimpleTest) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    aht10.o(i.AHT10_SimpleTest) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    aht10.o(i.AHT10_SimpleTest) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_SoftReset) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    aht10.o(i.AHT10_SoftReset) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    aht10.o(i.AHT10_SoftReset) refers to i2c.o(.bss) for hi2c1
    aht10.o(i.AHT10_SoftReset) refers to aht10.o(.data) for .data
    aht10.o(i.AHT10_TriggerMeasurement) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    aht10.o(i.AHT10_TriggerMeasurement) refers to aht10.o(.data) for .data
    aht10.o(i.AHT10_TriggerMeasurement) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_PowerOnInit) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Init) for OLED_Init
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteCommand) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_WriteData) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    oled.o(i.OLED_WriteData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteData) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteData) refers to i2c.o(.bss) for hi2c2
    ds1302.o(i.DS1302_DAT_Input) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ds1302.o(i.DS1302_DAT_Output) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ds1302.o(i.DS1302_GetTime) refers to ds1302.o(i.DS1302_ReadReg) for DS1302_ReadReg
    ds1302.o(i.DS1302_GetTime) refers to ds1302.o(i.DS1302_BCD2DEC) for DS1302_BCD2DEC
    ds1302.o(i.DS1302_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ds1302.o(i.DS1302_Init) refers to ds1302.o(i.DS1302_Delay_us) for DS1302_Delay_us
    ds1302.o(i.DS1302_Init) refers to ds1302.o(i.DS1302_WriteReg) for DS1302_WriteReg
    ds1302.o(i.DS1302_Init) refers to ds1302.o(i.DS1302_ReadReg) for DS1302_ReadReg
    ds1302.o(i.DS1302_IsClockRunning) refers to ds1302.o(i.DS1302_ReadReg) for DS1302_ReadReg
    ds1302.o(i.DS1302_IsPresent) refers to ds1302.o(i.DS1302_WriteReg) for DS1302_WriteReg
    ds1302.o(i.DS1302_IsPresent) refers to ds1302.o(i.DS1302_ReadReg) for DS1302_ReadReg
    ds1302.o(i.DS1302_ReadReg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ds1302.o(i.DS1302_ReadReg) refers to ds1302.o(i.DS1302_Delay_us) for DS1302_Delay_us
    ds1302.o(i.DS1302_ReadReg) refers to ds1302.o(i.DS1302_WriteByte) for DS1302_WriteByte
    ds1302.o(i.DS1302_ReadReg) refers to ds1302.o(i.DS1302_DAT_Input) for DS1302_DAT_Input
    ds1302.o(i.DS1302_ReadReg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ds1302.o(i.DS1302_SetTime) refers to ds1302.o(i.DS1302_WriteReg) for DS1302_WriteReg
    ds1302.o(i.DS1302_SetTime) refers to ds1302.o(i.DS1302_DEC2BCD) for DS1302_DEC2BCD
    ds1302.o(i.DS1302_WriteByte) refers to ds1302.o(i.DS1302_DAT_Output) for DS1302_DAT_Output
    ds1302.o(i.DS1302_WriteByte) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ds1302.o(i.DS1302_WriteByte) refers to ds1302.o(i.DS1302_Delay_us) for DS1302_Delay_us
    ds1302.o(i.DS1302_WriteReg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ds1302.o(i.DS1302_WriteReg) refers to ds1302.o(i.DS1302_Delay_us) for DS1302_Delay_us
    ds1302.o(i.DS1302_WriteReg) refers to ds1302.o(i.DS1302_WriteByte) for DS1302_WriteByte
    sgp30.o(i.SGP30_GetAirQuality) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    sgp30.o(i.SGP30_GetAirQuality) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    sgp30.o(i.SGP30_GetAirQuality) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    sgp30.o(i.SGP30_GetAirQuality) refers to sgp30.o(i.SGP30_CRC8) for SGP30_CRC8
    sgp30.o(i.SGP30_GetAirQuality) refers to sgp30.o(.data) for .data
    sgp30.o(i.SGP30_GetAirQuality) refers to i2c.o(.bss) for hi2c1
    sgp30.o(i.SGP30_GetBaseline) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    sgp30.o(i.SGP30_GetBaseline) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    sgp30.o(i.SGP30_GetBaseline) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    sgp30.o(i.SGP30_GetBaseline) refers to sgp30.o(i.SGP30_CRC8) for SGP30_CRC8
    sgp30.o(i.SGP30_GetBaseline) refers to sgp30.o(.data) for .data
    sgp30.o(i.SGP30_GetBaseline) refers to i2c.o(.bss) for hi2c1
    sgp30.o(i.SGP30_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    sgp30.o(i.SGP30_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    sgp30.o(i.SGP30_Init) refers to i2c.o(.bss) for hi2c1
    sgp30.o(i.SGP30_Init) refers to sgp30.o(.data) for .data
    sgp30.o(i.SGP30_IsPresent) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    sgp30.o(i.SGP30_IsPresent) refers to i2c.o(.bss) for hi2c1
    sgp30.o(i.SGP30_SetBaseline) refers to sgp30.o(i.SGP30_CRC8) for SGP30_CRC8
    sgp30.o(i.SGP30_SetBaseline) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    sgp30.o(i.SGP30_SetBaseline) refers to sgp30.o(.data) for .data
    sgp30.o(i.SGP30_SetBaseline) refers to i2c.o(.bss) for hi2c1
    sgp30.o(i.SGP30_SetHumidity) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    sgp30.o(i.SGP30_SetHumidity) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    sgp30.o(i.SGP30_SetHumidity) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    sgp30.o(i.SGP30_SetHumidity) refers to sgp30.o(i.SGP30_CRC8) for SGP30_CRC8
    sgp30.o(i.SGP30_SetHumidity) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    sgp30.o(i.SGP30_SetHumidity) refers to sgp30.o(.data) for .data
    sgp30.o(i.SGP30_SetHumidity) refers to i2c.o(.bss) for hi2c1
    weather_api.o(i.Weather_BuildURL) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    weather_api.o(i.Weather_BuildURL) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    weather_api.o(i.Weather_BuildURL) refers to _printf_str.o(.text) for _printf_str
    weather_api.o(i.Weather_BuildURL) refers to __2snprintf.o(.text) for __2snprintf
    weather_api.o(i.Weather_GetCachedData) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    weather_api.o(i.Weather_GetCachedData) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_GetData) refers to weather_api.o(i.Weather_GetDataByCity) for Weather_GetDataByCity
    weather_api.o(i.Weather_GetData) refers to weather_api.o(.data) for .data
    weather_api.o(i.Weather_GetDataByCity) refers to strlen.o(.text) for strlen
    weather_api.o(i.Weather_GetDataByCity) refers to weather_api.o(i.Weather_BuildURL) for Weather_BuildURL
    weather_api.o(i.Weather_GetDataByCity) refers to weather_api.o(i.Weather_HTTPGet) for Weather_HTTPGet
    weather_api.o(i.Weather_GetDataByCity) refers to weather_api.o(i.Weather_ParseJSON) for Weather_ParseJSON
    weather_api.o(i.Weather_GetDataByCity) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    weather_api.o(i.Weather_GetDataByCity) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    weather_api.o(i.Weather_GetDataByCity) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_GetDataByCity) refers to weather_api.o(.data) for .data
    weather_api.o(i.Weather_HTTPGet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    weather_api.o(i.Weather_HTTPGet) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    weather_api.o(i.Weather_HTTPGet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    weather_api.o(i.Weather_HTTPGet) refers to _printf_dec.o(.text) for _printf_int_dec
    weather_api.o(i.Weather_HTTPGet) refers to _printf_str.o(.text) for _printf_str
    weather_api.o(i.Weather_HTTPGet) refers to __2snprintf.o(.text) for __2snprintf
    weather_api.o(i.Weather_HTTPGet) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    weather_api.o(i.Weather_HTTPGet) refers to weather_api.o(i.Weather_SendHTTPRequest) for Weather_SendHTTPRequest
    weather_api.o(i.Weather_HTTPGet) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    weather_api.o(i.Weather_HTTPGet) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    weather_api.o(i.Weather_HTTPGet) refers to cmsis_os2.o(i.osDelay) for osDelay
    weather_api.o(i.Weather_HTTPGet) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    weather_api.o(i.Weather_Init) refers to strncpy.o(.text) for strncpy
    weather_api.o(i.Weather_Init) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_Init) refers to weather_api.o(.data) for .data
    weather_api.o(i.Weather_NeedUpdate) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    weather_api.o(i.Weather_ParseJSON) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    weather_api.o(i.Weather_ParseJSON) refers to json_parser.o(i.JSON_GetNestedFloat) for JSON_GetNestedFloat
    weather_api.o(i.Weather_ParseJSON) refers to json_parser.o(i.JSON_GetArrayFirstString) for JSON_GetArrayFirstString
    weather_api.o(i.Weather_ParseJSON) refers to weather_api.o(i.Weather_ParseWeatherStatus) for Weather_ParseWeatherStatus
    weather_api.o(i.Weather_ParseJSON) refers to json_parser.o(i.JSON_GetString) for JSON_GetString
    weather_api.o(i.Weather_ParseJSON) refers to json_parser.o(i.JSON_GetNestedString) for JSON_GetNestedString
    weather_api.o(i.Weather_ParseJSON) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    weather_api.o(i.Weather_ParseWeatherStatus) refers to strcmpv7m.o(.text) for strcmp
    weather_api.o(i.Weather_SendHTTPRequest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    weather_api.o(i.Weather_SendHTTPRequest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    weather_api.o(i.Weather_SendHTTPRequest) refers to _printf_str.o(.text) for _printf_str
    weather_api.o(i.Weather_SendHTTPRequest) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    weather_api.o(i.Weather_SendHTTPRequest) refers to _printf_dec.o(.text) for _printf_int_dec
    weather_api.o(i.Weather_SendHTTPRequest) refers to __2snprintf.o(.text) for __2snprintf
    weather_api.o(i.Weather_SendHTTPRequest) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    weather_api.o(i.Weather_SendHTTPRequest) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    weather_api.o(i.Weather_SendHTTPRequest) refers to weather_api.o(i.Weather_HTTPGet) for i.Weather_HTTPGet
    weather_api.o(i.Weather_SendHTTPRequest) refers to weather_api.o(.conststring) for .conststring
    weather_api.o(i.Weather_SendHTTPRequest) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_SendHTTPRequest) refers to usart.o(.bss) for huart1
    weather_api.o(i.Weather_SetAPIKey) refers to strlen.o(.text) for strlen
    weather_api.o(i.Weather_SetAPIKey) refers to strncpy.o(.text) for strncpy
    weather_api.o(i.Weather_SetAPIKey) refers to weather_api.o(.bss) for .bss
    weather_api.o(i.Weather_SetCity) refers to strlen.o(.text) for strlen
    weather_api.o(i.Weather_SetCity) refers to strncpy.o(.text) for strncpy
    weather_api.o(i.Weather_SetCity) refers to weather_api.o(.data) for .data
    wifi_manager.o(i.HAL_UART_RxCpltCallback) refers to wifi_manager.o(i.WiFi_ClearRxBuffer) for WiFi_ClearRxBuffer
    wifi_manager.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    wifi_manager.o(i.HAL_UART_RxCpltCallback) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.HAL_UART_RxCpltCallback) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    wifi_manager.o(i.WiFi_CheckModule) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    wifi_manager.o(i.WiFi_ClearRxBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    wifi_manager.o(i.WiFi_ClearRxBuffer) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_ClearRxBuffer) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_Connect) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    wifi_manager.o(i.WiFi_Connect) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    wifi_manager.o(i.WiFi_Connect) refers to _printf_str.o(.text) for _printf_str
    wifi_manager.o(i.WiFi_Connect) refers to __2snprintf.o(.text) for __2snprintf
    wifi_manager.o(i.WiFi_Connect) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    wifi_manager.o(i.WiFi_Connect) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    wifi_manager.o(i.WiFi_Connect) refers to wifi_manager.o(i.WiFi_GetLocalIP) for WiFi_GetLocalIP
    wifi_manager.o(i.WiFi_Connect) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_Connect) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_Disconnect) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    wifi_manager.o(i.WiFi_Disconnect) refers to rt_memclr.o(.text) for __aeabi_memclr
    wifi_manager.o(i.WiFi_Disconnect) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_Disconnect) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_GetLocalIP) refers to wifi_manager.o(i.WiFi_SendATCommandWithResponse) for WiFi_SendATCommandWithResponse
    wifi_manager.o(i.WiFi_GetLocalIP) refers to strstr.o(.text) for strstr
    wifi_manager.o(i.WiFi_GetLocalIP) refers to strchr.o(.text) for strchr
    wifi_manager.o(i.WiFi_GetLocalIP) refers to strncpy.o(.text) for strncpy
    wifi_manager.o(i.WiFi_Init) refers to cmsis_os2.o(i.osMutexNew) for osMutexNew
    wifi_manager.o(i.WiFi_Init) refers to cmsis_os2.o(i.osSemaphoreNew) for osSemaphoreNew
    wifi_manager.o(i.WiFi_Init) refers to wifi_manager.o(i.WiFi_ClearRxBuffer) for WiFi_ClearRxBuffer
    wifi_manager.o(i.WiFi_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    wifi_manager.o(i.WiFi_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    wifi_manager.o(i.WiFi_Init) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_Init) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_Init) refers to usart.o(.bss) for huart1
    wifi_manager.o(i.WiFi_Reset) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    wifi_manager.o(i.WiFi_Reset) refers to cmsis_os2.o(i.osDelay) for osDelay
    wifi_manager.o(i.WiFi_Reset) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_SendATCommand) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    wifi_manager.o(i.WiFi_SendATCommand) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    wifi_manager.o(i.WiFi_SendATCommand) refers to _printf_str.o(.text) for _printf_str
    wifi_manager.o(i.WiFi_SendATCommand) refers to cmsis_os2.o(i.osMutexAcquire) for osMutexAcquire
    wifi_manager.o(i.WiFi_SendATCommand) refers to wifi_manager.o(i.WiFi_ClearRxBuffer) for WiFi_ClearRxBuffer
    wifi_manager.o(i.WiFi_SendATCommand) refers to __2snprintf.o(.text) for __2snprintf
    wifi_manager.o(i.WiFi_SendATCommand) refers to strlen.o(.text) for strlen
    wifi_manager.o(i.WiFi_SendATCommand) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    wifi_manager.o(i.WiFi_SendATCommand) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    wifi_manager.o(i.WiFi_SendATCommand) refers to strstr.o(.text) for strstr
    wifi_manager.o(i.WiFi_SendATCommand) refers to cmsis_os2.o(i.osDelay) for osDelay
    wifi_manager.o(i.WiFi_SendATCommand) refers to cmsis_os2.o(i.osMutexRelease) for osMutexRelease
    wifi_manager.o(i.WiFi_SendATCommand) refers to wifi_manager.o(.data) for .data
    wifi_manager.o(i.WiFi_SendATCommand) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_SendATCommand) refers to usart.o(.bss) for huart1
    wifi_manager.o(i.WiFi_SendATCommandWithResponse) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    wifi_manager.o(i.WiFi_SendATCommandWithResponse) refers to strncpy.o(.text) for strncpy
    wifi_manager.o(i.WiFi_SendATCommandWithResponse) refers to wifi_manager.o(.bss) for .bss
    wifi_manager.o(i.WiFi_SetMode) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    wifi_manager.o(i.WiFi_SetMode) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    wifi_manager.o(i.WiFi_SetMode) refers to _printf_dec.o(.text) for _printf_int_dec
    wifi_manager.o(i.WiFi_SetMode) refers to __2snprintf.o(.text) for __2snprintf
    wifi_manager.o(i.WiFi_SetMode) refers to wifi_manager.o(i.WiFi_SendATCommand) for WiFi_SendATCommand
    json_parser.o(i.JSON_FindArray) refers to json_parser.o(i.JSON_FindKey) for JSON_FindKey
    json_parser.o(i.JSON_FindArray) refers to json_parser.o(i.JSON_FindValueStart) for JSON_FindValueStart
    json_parser.o(i.JSON_FindKey) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    json_parser.o(i.JSON_FindKey) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    json_parser.o(i.JSON_FindKey) refers to _printf_str.o(.text) for _printf_str
    json_parser.o(i.JSON_FindKey) refers to __2snprintf.o(.text) for __2snprintf
    json_parser.o(i.JSON_FindKey) refers to strstr.o(.text) for strstr
    json_parser.o(i.JSON_FindObject) refers to json_parser.o(i.JSON_FindKey) for JSON_FindKey
    json_parser.o(i.JSON_FindObject) refers to json_parser.o(i.JSON_FindValueStart) for JSON_FindValueStart
    json_parser.o(i.JSON_FindValueEnd) refers to json_parser.o(i.JSON_IsWhitespace) for JSON_IsWhitespace
    json_parser.o(i.JSON_FindValueStart) refers to strchr.o(.text) for strchr
    json_parser.o(i.JSON_FindValueStart) refers to json_parser.o(i.JSON_IsWhitespace) for JSON_IsWhitespace
    json_parser.o(i.JSON_GetArrayFirstString) refers to json_parser.o(i.JSON_FindArray) for JSON_FindArray
    json_parser.o(i.JSON_GetArrayFirstString) refers to json_parser.o(i.JSON_IsWhitespace) for JSON_IsWhitespace
    json_parser.o(i.JSON_GetArrayFirstString) refers to json_parser.o(i.JSON_GetString) for JSON_GetString
    json_parser.o(i.JSON_GetFloat) refers to json_parser.o(i.JSON_GetString) for JSON_GetString
    json_parser.o(i.JSON_GetFloat) refers to atof.o(i.atof) for atof
    json_parser.o(i.JSON_GetFloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    json_parser.o(i.JSON_GetInt) refers to json_parser.o(i.JSON_GetString) for JSON_GetString
    json_parser.o(i.JSON_GetInt) refers to atoi.o(.text) for atoi
    json_parser.o(i.JSON_GetNestedFloat) refers to json_parser.o(i.JSON_FindObject) for JSON_FindObject
    json_parser.o(i.JSON_GetNestedFloat) refers to json_parser.o(i.JSON_GetFloat) for JSON_GetFloat
    json_parser.o(i.JSON_GetNestedString) refers to json_parser.o(i.JSON_FindObject) for JSON_FindObject
    json_parser.o(i.JSON_GetNestedString) refers to json_parser.o(i.JSON_GetString) for JSON_GetString
    json_parser.o(i.JSON_GetString) refers to json_parser.o(i.JSON_FindKey) for JSON_FindKey
    json_parser.o(i.JSON_GetString) refers to json_parser.o(i.JSON_FindValueStart) for JSON_FindValueStart
    json_parser.o(i.JSON_GetString) refers to json_parser.o(i.JSON_FindValueEnd) for JSON_FindValueEnd
    json_parser.o(i.JSON_GetString) refers to strncpy.o(.text) for strncpy
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.Test_DS1302_Clock) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.Test_DS1302_Clock) refers to ds1302.o(i.DS1302_GetTime) for DS1302_GetTime
    main.o(i.Test_DS1302_Clock) refers to main.o(.data) for .data
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to ds1302.o(i.DS1302_Init) for DS1302_Init
    main.o(i.main) refers to ds1302.o(i.DS1302_GetTime) for DS1302_GetTime
    main.o(i.main) refers to ds1302.o(i.DS1302_SetTime) for DS1302_SetTime
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    main.o(i.main) refers to main.o(.constdata) for .constdata
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    freertos.o(i.Add_New_Alarm) refers to strncpy.o(.text) for strncpy
    freertos.o(i.Add_New_Alarm) refers to freertos.o(.data) for .data
    freertos.o(i.Buzzer_Key_Beep) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Buzzer_Key_Beep) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.Buzzer_Key_Beep) refers to freertos.o(.bss) for .bss
    freertos.o(i.Buzzer_Start_Alarm) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Buzzer_Start_Alarm) refers to freertos.o(.bss) for .bss
    freertos.o(i.Buzzer_Start_Timer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Buzzer_Start_Timer) refers to freertos.o(.bss) for .bss
    freertos.o(i.Buzzer_Stop) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.Buzzer_Stop) refers to freertos.o(.bss) for .bss
    freertos.o(i.Buzzer_Update) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Buzzer_Update) refers to freertos.o(i.Buzzer_Stop) for Buzzer_Stop
    freertos.o(i.Buzzer_Update) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.Buzzer_Update) refers to freertos.o(.bss) for .bss
    freertos.o(i.Check_Alarm) refers to freertos.o(i.Check_Multi_Alarms) for Check_Multi_Alarms
    freertos.o(i.Check_Multi_Alarms) refers to freertos.o(i.Check_Alarm_Repeat) for Check_Alarm_Repeat
    freertos.o(i.Check_Multi_Alarms) refers to freertos.o(i.Buzzer_Start_Alarm) for Buzzer_Start_Alarm
    freertos.o(i.Check_Multi_Alarms) refers to freertos.o(.data) for .data
    freertos.o(i.Decrease_Setting_Value) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Decrease_Setting_Value) refers to freertos.o(.data) for .data
    freertos.o(i.Decrease_Setting_Value) refers to freertos.o(.bss) for .bss
    freertos.o(i.Delete_Alarm) refers to rt_memclr.o(.text) for __aeabi_memclr
    freertos.o(i.Delete_Alarm) refers to strcpy.o(.text) for strcpy
    freertos.o(i.Delete_Alarm) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Alarm_Interface) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Alarm_Interface) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Alarm_Interface) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Alarm_Interface) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Alarm_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Alarm_Interface) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Alarm_Interface) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Alarm_Interface) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Alarm_Setting) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Alarm_Setting) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Alarm_Setting) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Alarm_Setting) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Alarm_Setting) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Alarm_Setting) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Alarm_Setting) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Alarm_Setting) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Alarm_Setting) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Alarm_Simple) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Alarm_Simple) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Alarm_Simple) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Alarm_Simple) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Alarm_Simple) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    freertos.o(i.Display_Alarm_Simple) refers to _printf_str.o(.text) for _printf_str
    freertos.o(i.Display_Alarm_Simple) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Alarm_Simple) refers to strcpy.o(.text) for strcpy
    freertos.o(i.Display_Alarm_Simple) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.Display_Alarm_Simple) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Alarm_Simple) refers to freertos.o(i.Display_Repeat_Pattern) for Display_Repeat_Pattern
    freertos.o(i.Display_Alarm_Simple) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Clock_Interface) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    freertos.o(i.Display_Clock_Interface) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Clock_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Clock_Interface) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.Display_Clock_Interface) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Data_Log) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Data_Log) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Data_Log) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Data_Log) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    freertos.o(i.Display_Data_Log) refers to _printf_str.o(.text) for _printf_str
    freertos.o(i.Display_Data_Log) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Data_Log) refers to strcpy.o(.text) for strcpy
    freertos.o(i.Display_Data_Log) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    freertos.o(i.Display_Data_Log) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Data_Log) refers to strcat.o(.text) for strcat
    freertos.o(i.Display_Data_Log) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.Display_Data_Log) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Data_Log) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Data_Log) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    freertos.o(i.Display_Multi_Alarm_Setting) refers to _printf_str.o(.text) for _printf_str
    freertos.o(i.Display_Multi_Alarm_Setting) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Multi_Alarm_Setting) refers to freertos.o(i.Display_Repeat_Pattern) for Display_Repeat_Pattern
    freertos.o(i.Display_Multi_Alarm_Setting) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.Display_Multi_Alarm_Setting) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Multi_Alarm_Setting) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Multi_Alarm_Setting) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Repeat_Pattern) refers to strcat.o(.text) for strcat
    freertos.o(i.Display_Repeat_Pattern) refers to strcpy.o(.text) for strcpy
    freertos.o(i.Display_Sensor_Interface) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    freertos.o(i.Display_Sensor_Interface) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    freertos.o(i.Display_Sensor_Interface) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    freertos.o(i.Display_Sensor_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Sensor_Interface) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    freertos.o(i.Display_Sensor_Interface) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.Display_Stopwatch_Interface) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Stopwatch_Interface) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Stopwatch_Interface) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Stopwatch_Interface) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Stopwatch_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Stopwatch_Interface) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Display_Stopwatch_Interface) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Stopwatch_Interface) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Stopwatch_Interface) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Time_Setting) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Time_Setting) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Time_Setting) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Time_Setting) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Time_Setting) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Time_Setting) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Time_Setting) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Time_Setting) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Timer_Interface) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Timer_Interface) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Timer_Interface) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Timer_Interface) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Timer_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Timer_Interface) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Timer_Interface) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Timer_Interface) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Timer_Setting) refers to _printf_pad.o(.text) for _printf_pre_padding
    freertos.o(i.Display_Timer_Setting) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Timer_Setting) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Timer_Setting) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Timer_Setting) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Timer_Setting) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Timer_Setting) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Timer_Setting) refers to freertos.o(.bss) for .bss
    freertos.o(i.Display_Timer_Setting) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Weather_Interface) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.Display_Weather_Interface) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    freertos.o(i.Display_Weather_Interface) refers to _printf_str.o(.text) for _printf_str
    freertos.o(i.Display_Weather_Interface) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    freertos.o(i.Display_Weather_Interface) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    freertos.o(i.Display_Weather_Interface) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    freertos.o(i.Display_Weather_Interface) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.Display_Weather_Interface) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.Display_Weather_Interface) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_Weather_Interface) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.Display_Weather_Interface) refers to weather_api.o(i.Weather_StatusToIcon) for Weather_StatusToIcon
    freertos.o(i.Display_Weather_Interface) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    freertos.o(i.Display_Weather_Interface) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Display_Weather_Interface) refers to strcpy.o(.text) for strcpy
    freertos.o(i.Display_Weather_Interface) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.Display_Weather_Interface) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.Display_Weather_Interface) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Weather_Interface) refers to freertos.o(.bss) for .bss
    freertos.o(i.Enter_Setting_Mode) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos.o(i.Enter_Setting_Mode) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Enter_Setting_Mode) refers to freertos.o(.data) for .data
    freertos.o(i.Enter_Setting_Mode) refers to freertos.o(.bss) for .bss
    freertos.o(i.Get_Next_Alarm) refers to freertos.o(.data) for .data
    freertos.o(i.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.HAL_GPIO_EXTI_Callback) refers to freertos.o(i.Buzzer_Stop) for Buzzer_Stop
    freertos.o(i.HAL_GPIO_EXTI_Callback) refers to freertos.o(.data) for .data
    freertos.o(i.HAL_GPIO_EXTI_Callback) refers to freertos.o(.bss) for .bss
    freertos.o(i.Handle_Key_Beep) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Handle_Key_Beep) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.Handle_Key_Beep) refers to freertos.o(i.Buzzer_Stop) for Buzzer_Stop
    freertos.o(i.Handle_Key_Beep) refers to freertos.o(.data) for .data
    freertos.o(i.Handle_Key_Beep) refers to freertos.o(.bss) for .bss
    freertos.o(i.Increase_Setting_Value) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Increase_Setting_Value) refers to freertos.o(.data) for .data
    freertos.o(i.Increase_Setting_Value) refers to freertos.o(.bss) for .bss
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osMutexNew) for osMutexNew
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osSemaphoreNew) for osSemaphoreNew
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osMessageQueueNew) for osMessageQueueNew
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDefaultTask) for StartDefaultTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartKeyTask) for StartKeyTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartSensorTask) for StartSensorTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDisplayTask) for StartDisplayTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartWiFiTask) for StartWiFiTask
    freertos.o(i.Next_Setting_Item) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Next_Setting_Item) refers to freertos.o(.data) for .data
    freertos.o(i.Record_Sensor_Data) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Record_Sensor_Data) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    freertos.o(i.Record_Sensor_Data) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    freertos.o(i.Record_Sensor_Data) refers to freertos.o(i.Update_Daily_Stats) for Update_Daily_Stats
    freertos.o(i.Record_Sensor_Data) refers to freertos.o(.bss) for .bss
    freertos.o(i.Record_Sensor_Data) refers to freertos.o(.data) for .data
    freertos.o(i.Reset_Daily_Stats) refers to freertos.o(.data) for .data
    freertos.o(i.Reset_Daily_Stats) refers to freertos.o(.bss) for .bss
    freertos.o(i.Save_And_Exit_Setting) refers to ds1302.o(i.DS1302_SetTime) for DS1302_SetTime
    freertos.o(i.Save_And_Exit_Setting) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    freertos.o(i.Save_And_Exit_Setting) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos.o(i.Save_And_Exit_Setting) refers to freertos.o(.data) for .data
    freertos.o(i.Save_And_Exit_Setting) refers to freertos.o(.bss) for .bss
    freertos.o(i.StartDefaultTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartDisplayTask) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos.o(i.StartDisplayTask) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos.o(i.StartDisplayTask) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_PowerOnInit) for OLED_PowerOnInit
    freertos.o(i.StartDisplayTask) refers to ds1302.o(i.DS1302_GetTime) for DS1302_GetTime
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Clock_Interface) for Display_Clock_Interface
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_Update) for OLED_Update
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Update_Timer) for Update_Timer
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Buzzer_Update) for Buzzer_Update
    freertos.o(i.StartDisplayTask) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osMutexAcquire) for osMutexAcquire
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osMessageQueueGet) for osMessageQueueGet
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.StartDisplayTask) refers to __2snprintf.o(.text) for __2snprintf
    freertos.o(i.StartDisplayTask) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    freertos.o(i.StartDisplayTask) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Time_Setting) for Display_Time_Setting
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Alarm_Setting) for Display_Alarm_Setting
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Multi_Alarm_Setting) for Display_Multi_Alarm_Setting
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Timer_Setting) for Display_Timer_Setting
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Check_Multi_Alarms) for Check_Multi_Alarms
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Record_Sensor_Data) for Record_Sensor_Data
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Sensor_Interface) for Display_Sensor_Interface
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Alarm_Simple) for Display_Alarm_Simple
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Data_Log) for Display_Data_Log
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Timer_Interface) for Display_Timer_Interface
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Stopwatch_Interface) for Display_Stopwatch_Interface
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Weather_Interface) for Display_Weather_Interface
    freertos.o(i.StartDisplayTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartDisplayTask) refers to freertos.o(.bss) for .bss
    freertos.o(i.StartKeyTask) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.StartKeyTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartKeyTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Increase_Setting_Value) for Increase_Setting_Value
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Decrease_Setting_Value) for Decrease_Setting_Value
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Save_And_Exit_Setting) for Save_And_Exit_Setting
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Buzzer_Stop) for Buzzer_Stop
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Enter_Setting_Mode) for Enter_Setting_Mode
    freertos.o(i.StartKeyTask) refers to freertos.o(i.Next_Setting_Item) for Next_Setting_Item
    freertos.o(i.StartKeyTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartKeyTask) refers to freertos.o(.bss) for .bss
    freertos.o(i.StartSensorTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartSensorTask) refers to aht10.o(i.AHT10_SimpleTest) for AHT10_SimpleTest
    freertos.o(i.StartSensorTask) refers to aht10.o(i.AHT10_Init) for AHT10_Init
    freertos.o(i.StartSensorTask) refers to sgp30.o(i.SGP30_IsPresent) for SGP30_IsPresent
    freertos.o(i.StartSensorTask) refers to sgp30.o(i.SGP30_Init) for SGP30_Init
    freertos.o(i.StartSensorTask) refers to cmsis_os2.o(i.osMessageQueuePut) for osMessageQueuePut
    freertos.o(i.StartSensorTask) refers to aht10.o(i.AHT10_GetTemperatureHumidity) for AHT10_GetTemperatureHumidity
    freertos.o(i.StartSensorTask) refers to sgp30.o(i.SGP30_GetAirQuality) for SGP30_GetAirQuality
    freertos.o(i.StartSensorTask) refers to sgp30.o(i.SGP30_SetHumidity) for SGP30_SetHumidity
    freertos.o(i.StartSensorTask) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    freertos.o(i.StartSensorTask) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    freertos.o(i.StartSensorTask) refers to aht10.o(.data) for aht10_calibrated
    freertos.o(i.StartSensorTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartSensorTask) refers to sgp30.o(.data) for sgp30_initialized
    freertos.o(i.StartWiFiTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartWiFiTask) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.StartWiFiTask) refers to strlen.o(.text) for strlen
    freertos.o(i.StartWiFiTask) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    freertos.o(i.StartWiFiTask) refers to wifi_manager.o(i.WiFi_Init) for WiFi_Init
    freertos.o(i.StartWiFiTask) refers to wifi_manager.o(i.WiFi_CheckModule) for WiFi_CheckModule
    freertos.o(i.StartWiFiTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartWiFiTask) refers to usart.o(.bss) for huart1
    freertos.o(i.Update_Daily_Stats) refers to freertos.o(.bss) for .bss
    freertos.o(i.Update_Daily_Stats) refers to freertos.o(.data) for .data
    freertos.o(i.Update_Timer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.Update_Timer) refers to freertos.o(i.Buzzer_Start_Timer) for Buzzer_Start_Timer
    freertos.o(i.Update_Timer) refers to freertos.o(.bss) for .bss
    freertos.o(i.Update_Timer) refers to freertos.o(.data) for .data
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.EXTI15_10_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim4
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to freertos.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to wifi_manager.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to wifi_manager.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for .data
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for .data
    cmsis_os2.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osKernelGetInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetCount) for OS_Tick_GetCount
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.AllocBlock) for AllocBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.CreateBlock) for CreateBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osMemoryPoolNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.__ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.__ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.__ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing aht10.o(.rev16_text), (4 bytes).
    Removing aht10.o(.revsh_text), (4 bytes).
    Removing aht10.o(.rrx_text), (6 bytes).
    Removing aht10.o(i.AHT10_CRC8), (50 bytes).
    Removing aht10.o(i.AHT10_I2C_Scan), (68 bytes).
    Removing aht10.o(i.AHT10_IsBusy), (22 bytes).
    Removing aht10.o(i.AHT10_IsCalibrated), (12 bytes).
    Removing aht10.o(i.AHT10_IsPresent), (48 bytes).
    Removing aht10.o(i.AHT10_ReadData), (156 bytes).
    Removing aht10.o(i.AHT10_ReadRawData), (36 bytes).
    Removing aht10.o(i.AHT10_ReadStatus), (36 bytes).
    Removing aht10.o(i.AHT10_SoftReset), (56 bytes).
    Removing aht10.o(i.AHT10_TriggerMeasurement), (56 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DrawArc), (586 bytes).
    Removing oled.o(i.OLED_DrawCircle), (318 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (692 bytes).
    Removing oled.o(i.OLED_DrawLine), (234 bytes).
    Removing oled.o(i.OLED_DrawPoint), (52 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (116 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (196 bytes).
    Removing oled.o(i.OLED_GetPoint), (56 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (2 bytes).
    Removing oled.o(i.OLED_I2C_Start), (2 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (2 bytes).
    Removing oled.o(i.OLED_IsInAngle), (96 bytes).
    Removing oled.o(i.OLED_Pow), (14 bytes).
    Removing oled.o(i.OLED_Printf), (42 bytes).
    Removing oled.o(i.OLED_Reverse), (40 bytes).
    Removing oled.o(i.OLED_ReverseArea), (88 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (64 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (190 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (72 bytes).
    Removing oled.o(i.OLED_ShowNum), (70 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (94 bytes).
    Removing oled.o(i.OLED_UpdateArea), (96 bytes).
    Removing oled.o(i.OLED_W_SCL), (2 bytes).
    Removing oled.o(i.OLED_W_SDA), (2 bytes).
    Removing oled.o(i.OLED_pnpoly), (116 bytes).
    Removing oled_data.o(.constdata), (32 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing ds1302.o(.rev16_text), (4 bytes).
    Removing ds1302.o(.revsh_text), (4 bytes).
    Removing ds1302.o(.rrx_text), (6 bytes).
    Removing ds1302.o(i.DS1302_IsClockRunning), (16 bytes).
    Removing ds1302.o(i.DS1302_IsPresent), (46 bytes).
    Removing sgp30.o(.rev16_text), (4 bytes).
    Removing sgp30.o(.revsh_text), (4 bytes).
    Removing sgp30.o(.rrx_text), (6 bytes).
    Removing sgp30.o(i.SGP30_GetBaseline), (144 bytes).
    Removing sgp30.o(i.SGP30_SetBaseline), (116 bytes).
    Removing weather_api.o(.rev16_text), (4 bytes).
    Removing weather_api.o(.revsh_text), (4 bytes).
    Removing weather_api.o(.rrx_text), (6 bytes).
    Removing weather_api.o(i.Weather_BuildURL), (132 bytes).
    Removing weather_api.o(i.Weather_GetCachedData), (36 bytes).
    Removing weather_api.o(i.Weather_GetData), (12 bytes).
    Removing weather_api.o(i.Weather_GetDataByCity), (128 bytes).
    Removing weather_api.o(i.Weather_HTTPGet), (152 bytes).
    Removing weather_api.o(i.Weather_Init), (60 bytes).
    Removing weather_api.o(i.Weather_NeedUpdate), (28 bytes).
    Removing weather_api.o(i.Weather_ParseJSON), (248 bytes).
    Removing weather_api.o(i.Weather_ParseWeatherStatus), (164 bytes).
    Removing weather_api.o(i.Weather_SendHTTPRequest), (116 bytes).
    Removing weather_api.o(i.Weather_SetAPIKey), (88 bytes).
    Removing weather_api.o(i.Weather_SetCity), (40 bytes).
    Removing weather_api.o(i.Weather_StatusToString), (100 bytes).
    Removing weather_api.o(.bss), (1192 bytes).
    Removing weather_api.o(.conststring), (77 bytes).
    Removing weather_api.o(.data), (36 bytes).
    Removing wifi_manager.o(.rev16_text), (4 bytes).
    Removing wifi_manager.o(.revsh_text), (4 bytes).
    Removing wifi_manager.o(.rrx_text), (6 bytes).
    Removing wifi_manager.o(i.WiFi_Connect), (108 bytes).
    Removing wifi_manager.o(i.WiFi_Disconnect), (64 bytes).
    Removing wifi_manager.o(i.WiFi_GetLocalIP), (108 bytes).
    Removing wifi_manager.o(i.WiFi_Reset), (48 bytes).
    Removing wifi_manager.o(i.WiFi_SendATCommandWithResponse), (44 bytes).
    Removing wifi_manager.o(i.WiFi_SetMode), (56 bytes).
    Removing json_parser.o(.rev16_text), (4 bytes).
    Removing json_parser.o(.revsh_text), (4 bytes).
    Removing json_parser.o(.rrx_text), (6 bytes).
    Removing json_parser.o(i.JSON_FindArray), (26 bytes).
    Removing json_parser.o(i.JSON_FindKey), (48 bytes).
    Removing json_parser.o(i.JSON_FindObject), (26 bytes).
    Removing json_parser.o(i.JSON_FindValueEnd), (152 bytes).
    Removing json_parser.o(i.JSON_FindValueStart), (36 bytes).
    Removing json_parser.o(i.JSON_GetArrayFirstString), (72 bytes).
    Removing json_parser.o(i.JSON_GetFloat), (46 bytes).
    Removing json_parser.o(i.JSON_GetInt), (42 bytes).
    Removing json_parser.o(i.JSON_GetNestedFloat), (40 bytes).
    Removing json_parser.o(i.JSON_GetNestedString), (44 bytes).
    Removing json_parser.o(i.JSON_GetString), (94 bytes).
    Removing json_parser.o(i.JSON_IsWhitespace), (24 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.Test_DS1302_Clock), (40 bytes).
    Removing main.o(.data), (12 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing freertos.o(i.Add_New_Alarm), (92 bytes).
    Removing freertos.o(i.Buzzer_Key_Beep), (80 bytes).
    Removing freertos.o(i.Check_Alarm), (4 bytes).
    Removing freertos.o(i.Delete_Alarm), (64 bytes).
    Removing freertos.o(i.Display_Alarm_Interface), (160 bytes).
    Removing freertos.o(i.Get_Next_Alarm), (80 bytes).
    Removing freertos.o(i.Handle_Key_Beep), (88 bytes).
    Removing freertos.o(i.Reset_Daily_Stats), (28 bytes).
    Removing freertos.o(i.Update_Stopwatch), (2 bytes).
    Removing freertos.o(.data), (4 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (56 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (68 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_InitTick), (64 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (400 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (64 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (44 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupSync), (204 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (36 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (22 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (40 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (28 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (30 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (156 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (30 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (34 bytes).
    Removing queue.o(i.xQueuePeek), (308 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (116 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (140 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (58 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (130 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (60 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (8 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (34 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (108 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (122 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (48 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (78 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (212 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (144 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (258 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (142 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (38 bytes).
    Removing tasks.o(i.eTaskGetState), (116 bytes).
    Removing tasks.o(i.pcTaskGetName), (32 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (88 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (20 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.ulTaskNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (16 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (172 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (140 bytes).
    Removing tasks.o(i.vTaskDelete), (144 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskGetInfo), (116 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (176 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (180 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (48 bytes).
    Removing tasks.o(i.vTaskSuspend), (156 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (224 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (260 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (140 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (136 bytes).
    Removing timers.o(i.pcTimerGetName), (22 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (78 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (36 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (48 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (54 bytes).
    Removing timers.o(i.vTimerSetTimerID), (38 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (52 bytes).
    Removing timers.o(i.xTimerCreateStatic), (46 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (22 bytes).
    Removing timers.o(i.xTimerGetPeriod), (22 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (48 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (60 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.AllocBlock), (18 bytes).
    Removing cmsis_os2.o(i.CreateBlock), (26 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetCount), (12 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (24 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (46 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (62 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (32 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (22 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (44 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (84 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (98 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (32 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (66 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (14 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (42 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (66 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (58 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolAlloc), (144 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolDelete), (94 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolFree), (180 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetBlockSize), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCapacity), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCount), (52 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetName), (18 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetSpace), (40 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolNew), (228 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (42 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (44 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (34 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (44 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (20 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (92 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (42 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (84 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (98 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (8 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (78 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (44 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (116 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (148 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (14 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (4 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (16 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (18 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (22 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (52 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (32 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (40 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (32 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (52 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (36 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (68 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (16 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (16 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (120 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (50 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (68 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (108 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).

740 unused section(s) (total 63465 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ..\Core\Src\AHT10.c                      0x00000000   Number         0  aht10.o ABSOLUTE
    ..\Core\Src\DS1302.c                     0x00000000   Number         0  ds1302.o ABSOLUTE
    ..\Core\Src\OLED.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\Core\Src\OLED_Data.c                  0x00000000   Number         0  oled_data.o ABSOLUTE
    ..\Core\Src\SGP30.c                      0x00000000   Number         0  sgp30.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\json_parser.c                0x00000000   Number         0  json_parser.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Core\Src\weather_api.c                0x00000000   Number         0  weather_api.o ABSOLUTE
    ..\Core\Src\wifi_manager.c               0x00000000   Number         0  wifi_manager.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\Core\\Src\\AHT10.c                   0x00000000   Number         0  aht10.o ABSOLUTE
    ..\\Core\\Src\\DS1302.c                  0x00000000   Number         0  ds1302.o ABSOLUTE
    ..\\Core\\Src\\OLED.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Core\\Src\\SGP30.c                   0x00000000   Number         0  sgp30.o ABSOLUTE
    ..\\Core\\Src\\json_parser.c             0x00000000   Number         0  json_parser.o ABSOLUTE
    ..\\Core\\Src\\weather_api.c             0x00000000   Number         0  weather_api.o ABSOLUTE
    ..\\Core\\Src\\wifi_manager.c            0x00000000   Number         0  wifi_manager.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x08000128   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000184   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a0   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001a0   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x080001a6   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000013  0x080001ac   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001b2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x080001b8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001bc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001be   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001c4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001c4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001c4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080001c4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001ce   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001ce   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001d0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001d2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001d2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001d2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001d2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001d2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001d2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001d4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001d4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001d4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001da   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001da   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001de   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001de   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001e6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001e8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001e8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001ec   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x080001f4   Section      150  port.o(.emb_text)
    .text                                    0x0800028c   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x080002cc   Section        0  __2snprintf.o(.text)
    .text                                    0x08000304   Section        0  _printf_pad.o(.text)
    .text                                    0x08000352   Section        0  _printf_str.o(.text)
    .text                                    0x080003a4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800041c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080005a4   Section        0  strstr.o(.text)
    .text                                    0x080005c8   Section        0  strcpy.o(.text)
    .text                                    0x08000610   Section        0  strlen.o(.text)
    .text                                    0x0800064e   Section        0  strcat.o(.text)
    .text                                    0x08000666   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080006f0   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000754   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000764   Section       68  rt_memclr.o(.text)
    .text                                    0x080007a8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080007f8   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000878   Section        0  heapauxi.o(.text)
    .text                                    0x0800087e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000930   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000933   Thumb Code   428  _printf_fp_dec.o(.text)
    .text                                    0x08000d4c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000d4d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000d7c   Section        0  _sputc.o(.text)
    .text                                    0x08000d86   Section        0  _snputc.o(.text)
    .text                                    0x08000d96   Section        0  _printf_char.o(.text)
    .text                                    0x08000dc4   Section        8  libspace.o(.text)
    .text                                    0x08000dcc   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000dd4   Section      138  lludiv10.o(.text)
    .text                                    0x08000e60   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000ee0   Section        0  bigflt0.o(.text)
    .text                                    0x08000fbc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001006   Section        0  exit.o(.text)
    .text                                    0x08001014   Section        0  sys_exit.o(.text)
    .text                                    0x08001020   Section        2  use_no_semi.o(.text)
    .text                                    0x08001022   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08001022   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001060   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080010a6   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001106   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800143e   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001504   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800152c   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001554   Section      580  btod.o(CL$$btod_mult_common)
    i.AHT10_GetTemperatureHumidity           0x08001798   Section        0  aht10.o(i.AHT10_GetTemperatureHumidity)
    i.AHT10_Init                             0x08001868   Section        0  aht10.o(i.AHT10_Init)
    i.AHT10_SimpleTest                       0x080018ac   Section        0  aht10.o(i.AHT10_SimpleTest)
    i.BusFault_Handler                       0x080018dc   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.Buzzer_Start_Alarm                     0x080018e0   Section        0  freertos.o(i.Buzzer_Start_Alarm)
    i.Buzzer_Start_Timer                     0x08001904   Section        0  freertos.o(i.Buzzer_Start_Timer)
    i.Buzzer_Stop                            0x08001928   Section        0  freertos.o(i.Buzzer_Stop)
    i.Buzzer_Update                          0x08001944   Section        0  freertos.o(i.Buzzer_Update)
    i.Check_Alarm_Repeat                     0x080019d8   Section        0  freertos.o(i.Check_Alarm_Repeat)
    i.Check_Multi_Alarms                     0x080019f0   Section        0  freertos.o(i.Check_Multi_Alarms)
    i.DS1302_BCD2DEC                         0x08001a5c   Section        0  ds1302.o(i.DS1302_BCD2DEC)
    i.DS1302_DAT_Input                       0x08001a70   Section        0  ds1302.o(i.DS1302_DAT_Input)
    i.DS1302_DAT_Output                      0x08001a90   Section        0  ds1302.o(i.DS1302_DAT_Output)
    i.DS1302_DEC2BCD                         0x08001ab0   Section        0  ds1302.o(i.DS1302_DEC2BCD)
    i.DS1302_Delay_us                        0x08001ac6   Section        0  ds1302.o(i.DS1302_Delay_us)
    DS1302_Delay_us                          0x08001ac7   Thumb Code    22  ds1302.o(i.DS1302_Delay_us)
    i.DS1302_GetTime                         0x08001adc   Section        0  ds1302.o(i.DS1302_GetTime)
    i.DS1302_Init                            0x08001b48   Section        0  ds1302.o(i.DS1302_Init)
    i.DS1302_ReadReg                         0x08001bb4   Section        0  ds1302.o(i.DS1302_ReadReg)
    i.DS1302_SetTime                         0x08001c44   Section        0  ds1302.o(i.DS1302_SetTime)
    i.DS1302_WriteByte                       0x08001d08   Section        0  ds1302.o(i.DS1302_WriteByte)
    DS1302_WriteByte                         0x08001d09   Thumb Code    80  ds1302.o(i.DS1302_WriteByte)
    i.DS1302_WriteReg                        0x08001d5c   Section        0  ds1302.o(i.DS1302_WriteReg)
    i.DebugMon_Handler                       0x08001db4   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Decrease_Setting_Value                 0x08001db8   Section        0  freertos.o(i.Decrease_Setting_Value)
    i.Display_Alarm_Setting                  0x08001f28   Section        0  freertos.o(i.Display_Alarm_Setting)
    i.Display_Alarm_Simple                   0x08002030   Section        0  freertos.o(i.Display_Alarm_Simple)
    i.Display_Clock_Interface                0x08002144   Section        0  freertos.o(i.Display_Clock_Interface)
    i.Display_Data_Log                       0x080022fc   Section        0  freertos.o(i.Display_Data_Log)
    i.Display_Multi_Alarm_Setting            0x08002654   Section        0  freertos.o(i.Display_Multi_Alarm_Setting)
    i.Display_Repeat_Pattern                 0x08002770   Section        0  freertos.o(i.Display_Repeat_Pattern)
    i.Display_Sensor_Interface               0x08002830   Section        0  freertos.o(i.Display_Sensor_Interface)
    i.Display_Stopwatch_Interface            0x08002a90   Section        0  freertos.o(i.Display_Stopwatch_Interface)
    i.Display_Time_Setting                   0x08002b74   Section        0  freertos.o(i.Display_Time_Setting)
    i.Display_Timer_Interface                0x08002ce0   Section        0  freertos.o(i.Display_Timer_Interface)
    i.Display_Timer_Setting                  0x08002dd4   Section        0  freertos.o(i.Display_Timer_Setting)
    i.Display_Weather_Interface              0x08002ef8   Section        0  freertos.o(i.Display_Weather_Interface)
    i.EXTI15_10_IRQHandler                   0x080030c8   Section        0  stm32f1xx_it.o(i.EXTI15_10_IRQHandler)
    i.Enter_Setting_Mode                     0x080030f0   Section        0  freertos.o(i.Enter_Setting_Mode)
    i.Error_Handler                          0x08003168   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x0800316c   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080031b4   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x0800324c   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x08003270   Section        0  freertos.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080032d4   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080032ec   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080034cc   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080034d6   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080034e0   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x080034ec   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_IsDeviceReady                  0x08003674   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    i.HAL_I2C_Master_Receive                 0x080037e0   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x08003a30   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08003b5c   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08003bf4   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003c04   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003c28   Section        0  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003cc0   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003d08   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003d24   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003d64   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08003d88   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08003eb4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08003ef4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003f14   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003f34   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003f80   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_TIMEx_BreakCallback                0x080042a0   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080042a2   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x080042a4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080042fe   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08004300   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08004358   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x0800435a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x0800448a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800448c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08004490   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x080044a4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x080044a6   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x080044a8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080044ac   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004718   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x0800477c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080047fc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004818   Section        0  wifi_manager.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x0800485c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080048fc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080048fe   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08004900   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08004901   Thumb Code    46  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestRead                  0x08004930   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead)
    I2C_MasterRequestRead                    0x08004931   Thumb Code   230  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead)
    i.I2C_MasterRequestWrite                 0x08004a1c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08004a1d   Thumb Code   150  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004ab8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004ab9   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08004b10   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08004b11   Thumb Code   144  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08004ba0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08004ba1   Thumb Code   188  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08004c5c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004c5d   Thumb Code   112  stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08004ccc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08004ccd   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.Increase_Setting_Value                 0x08004d24   Section        0  freertos.o(i.Increase_Setting_Value)
    i.MX_FREERTOS_Init                       0x08004e98   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x08004f38   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x0800500c   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x0800504c   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_USART1_UART_Init                    0x0800508c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x080050c4   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080050c6   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.Next_Setting_Item                      0x080050c8   Section        0  freertos.o(i.Next_Setting_Item)
    i.OLED_Clear                             0x0800511c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08005144   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_Init                              0x080051a0   Section        0  oled.o(i.OLED_Init)
    i.OLED_PowerOnInit                       0x08005256   Section        0  oled.o(i.OLED_PowerOnInit)
    i.OLED_SetCursor                         0x08005274   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08005298   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x080052d4   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08005390   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x080053bc   Section        0  oled.o(i.OLED_Update)
    i.OLED_WriteCommand                      0x080053e4   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08005408   Section        0  oled.o(i.OLED_WriteData)
    i.Record_Sensor_Data                     0x0800543c   Section        0  freertos.o(i.Record_Sensor_Data)
    i.SGP30_CRC8                             0x080054d0   Section        0  sgp30.o(i.SGP30_CRC8)
    i.SGP30_GetAirQuality                    0x08005504   Section        0  sgp30.o(i.SGP30_GetAirQuality)
    i.SGP30_Init                             0x08005598   Section        0  sgp30.o(i.SGP30_Init)
    i.SGP30_IsPresent                        0x080055dc   Section        0  sgp30.o(i.SGP30_IsPresent)
    i.SGP30_SetHumidity                      0x080055f8   Section        0  sgp30.o(i.SGP30_SetHumidity)
    i.Save_And_Exit_Setting                  0x08005660   Section        0  freertos.o(i.Save_And_Exit_Setting)
    i.StartDefaultTask                       0x080056ec   Section        0  freertos.o(i.StartDefaultTask)
    i.StartDisplayTask                       0x080056f8   Section        0  freertos.o(i.StartDisplayTask)
    i.StartKeyTask                           0x080059dc   Section        0  freertos.o(i.StartKeyTask)
    i.StartSensorTask                        0x08005be8   Section        0  freertos.o(i.StartSensorTask)
    i.StartWiFiTask                          0x08005d64   Section        0  freertos.o(i.StartWiFiTask)
    i.SysTick_Handler                        0x08005e20   Section        0  cmsis_os2.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005e3a   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005e98   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM4_IRQHandler                        0x08005e9c   Section        0  stm32f1xx_it.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x08005ea8   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UART_DMAAbortOnError                   0x08005f20   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005f21   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08005f30   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005f31   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08005f7e   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005f7f   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006040   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006041   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x080060f8   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x0800612e   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x0800612f   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080061a0   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.Update_Daily_Stats                     0x080061ac   Section        0  freertos.o(i.Update_Daily_Stats)
    i.Update_Timer                           0x0800621c   Section        0  freertos.o(i.Update_Timer)
    i.UsageFault_Handler                     0x0800626c   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.Weather_StatusToIcon                   0x0800626e   Section        0  weather_api.o(i.Weather_StatusToIcon)
    i.WiFi_CheckModule                       0x0800629c   Section        0  wifi_manager.o(i.WiFi_CheckModule)
    i.WiFi_ClearRxBuffer                     0x080062b8   Section        0  wifi_manager.o(i.WiFi_ClearRxBuffer)
    WiFi_ClearRxBuffer                       0x080062b9   Thumb Code    22  wifi_manager.o(i.WiFi_ClearRxBuffer)
    i.WiFi_Init                              0x080062d8   Section        0  wifi_manager.o(i.WiFi_Init)
    i.WiFi_SendATCommand                     0x0800632c   Section        0  wifi_manager.o(i.WiFi_SendATCommand)
    i.__ARM_fpclassify                       0x080063ec   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08006414   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08006415   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x08006434   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x08006444   Section        0  main.o(i.main)
    i.osDelay                                0x0800649c   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x080064b8   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x080064dc   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osMessageQueueGet                      0x08006514   Section        0  cmsis_os2.o(i.osMessageQueueGet)
    i.osMessageQueueNew                      0x08006578   Section        0  cmsis_os2.o(i.osMessageQueueNew)
    i.osMessageQueuePut                      0x080065d0   Section        0  cmsis_os2.o(i.osMessageQueuePut)
    i.osMutexAcquire                         0x08006638   Section        0  cmsis_os2.o(i.osMutexAcquire)
    i.osMutexNew                             0x0800668a   Section        0  cmsis_os2.o(i.osMutexNew)
    i.osMutexRelease                         0x080066f2   Section        0  cmsis_os2.o(i.osMutexRelease)
    i.osSemaphoreNew                         0x08006734   Section        0  cmsis_os2.o(i.osSemaphoreNew)
    i.osThreadNew                            0x080067cc   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x08006854   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08006855   Thumb Code    84  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x080068b0   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x080068b1   Thumb Code   190  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x08006980   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x08006981   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCopyDataFromQueue                   0x080069d8   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x080069d9   Thumb Code    38  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x080069fe   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x080069ff   Thumb Code   108  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x08006a6a   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08006a6b   Thumb Code    52  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x08006aa0   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08006aa1   Thumb Code    66  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08006aec   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08006aed   Thumb Code    82  tasks.o(i.prvIdleTask)
    i.prvInitialiseMutex                     0x08006b4c   Section        0  queue.o(i.prvInitialiseMutex)
    prvInitialiseMutex                       0x08006b4d   Thumb Code    22  queue.o(i.prvInitialiseMutex)
    i.prvInitialiseNewQueue                  0x08006b64   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08006b65   Thumb Code    34  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08006b88   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08006b89   Thumb Code   176  tasks.o(i.prvInitialiseNewTask)
    i.prvInsertBlockIntoFreeList             0x08006c38   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08006c39   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08006c84   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08006c85   Thumb Code    52  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08006cbc   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08006cbd   Thumb Code    28  queue.o(i.prvIsQueueEmpty)
    i.prvProcessReceivedCommands             0x08006cd8   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08006cd9   Thumb Code   248  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08006dd4   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08006dd5   Thumb Code   182  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08006e94   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08006e95   Thumb Code    26  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08006eb4   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08006eb5   Thumb Code    36  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08006edc   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08006edd   Thumb Code   104  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08006f48   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08006f49   Thumb Code    36  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08006f70   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08006f71   Thumb Code    32  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08006f94   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08006f95   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08007000   Section        0  heap_4.o(i.pvPortMalloc)
    i.pvTaskIncrementMutexHeldCount          0x080070dc   Section        0  tasks.o(i.pvTaskIncrementMutexHeldCount)
    i.pxPortInitialiseStack                  0x080070f4   Section        0  port.o(i.pxPortInitialiseStack)
    i.uxListRemove                           0x08007118   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08007140   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x08007154   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x0800716c   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08007182   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08007188   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x080071b8   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x080071d0   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08007210   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08007238   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x0800729c   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x080072c0   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x08007314   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueDelete                           0x0800733c   Section        0  queue.o(i.vQueueDelete)
    i.vQueueUnregisterQueue                  0x0800736c   Section        0  queue.o(i.vQueueUnregisterQueue)
    i.vQueueWaitForMessageRestricted         0x08007394   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x080073d8   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08007424   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08007434   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08007440   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08007470   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskPriorityDisinheritAfterTimeout    0x080074a8   Section        0  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    i.vTaskStartScheduler                    0x08007538   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x080075c0   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x080075d0   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x08007634   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x080076dc   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueCreateCountingSemaphore          0x08007708   Section        0  queue.o(i.xQueueCreateCountingSemaphore)
    i.xQueueCreateCountingSemaphoreStatic    0x08007742   Section        0  queue.o(i.xQueueCreateCountingSemaphoreStatic)
    i.xQueueCreateMutex                      0x08007782   Section        0  queue.o(i.xQueueCreateMutex)
    i.xQueueCreateMutexStatic                0x08007798   Section        0  queue.o(i.xQueueCreateMutexStatic)
    i.xQueueGenericCreate                    0x080077b2   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueGenericCreateStatic              0x080077f4   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x0800785c   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x080078e4   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08007a44   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueGiveMutexRecursive               0x08007b02   Section        0  queue.o(i.xQueueGiveMutexRecursive)
    i.xQueueReceive                          0x08007b40   Section        0  queue.o(i.xQueueReceive)
    i.xQueueReceiveFromISR                   0x08007c78   Section        0  queue.o(i.xQueueReceiveFromISR)
    i.xQueueSemaphoreTake                    0x08007d14   Section        0  queue.o(i.xQueueSemaphoreTake)
    i.xQueueTakeMutexRecursive               0x08007e8c   Section        0  queue.o(i.xQueueTakeMutexRecursive)
    i.xTaskCheckForTimeOut                   0x08007ecc   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08007f40   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08007f9a   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetCurrentTaskHandle              0x08007ff0   Section        0  tasks.o(i.xTaskGetCurrentTaskHandle)
    i.xTaskGetSchedulerState                 0x08007ffc   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08008018   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08008024   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x080080ec   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskPriorityInherit                   0x0800816c   Section        0  tasks.o(i.xTaskPriorityInherit)
    i.xTaskRemoveFromEventList               0x080081e4   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x08008254   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08008318   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08008378   Section        0  timers.o(i.xTimerGenericCommand)
    locale$$code                             0x080083e0   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x0800840c   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dcheck1                            0x08008470   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dfltu                              0x08008480   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x080084a8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080085fc   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08008698   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x080086a4   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x080086fc   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x0800870b   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x080087c0   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$fdiv                               0x080087cc   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080087cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x08008950   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08008988   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$ffltu                              0x080089c8   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fmul                               0x080089f0   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08008af2   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08008b7e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x08008b88   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08008b97   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x08008c72   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x08008c76   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08008cda   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scalbnf                            0x08008d36   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$trapveneer                         0x08008d82   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08008db2   Section     1520  oled_data.o(.constdata)
    x$fpl$usenofp                            0x08008db2   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080093a2   Section      570  oled_data.o(.constdata)
    .constdata                               0x080095dc   Section        8  main.o(.constdata)
    .constdata                               0x080095e4   Section      356  freertos.o(.constdata)
    .constdata                               0x08009748   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08009748   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x0800974a   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800975a   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x0800976a   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x08009772   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009772   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08009784   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08009784   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080097c0   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08009818   Section      145  freertos.o(.conststring)
    locale$$data                             0x080098cc   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080098d0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080098d8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080098e4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080098e6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080098e7   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x080098e8   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        1  aht10.o(.data)
    .data                                    0x20000001   Section        1  sgp30.o(.data)
    .data                                    0x20000004   Section       12  wifi_manager.o(.data)
    g_wifi_status                            0x20000004   Data           1  wifi_manager.o(.data)
    g_rx_complete                            0x20000005   Data           1  wifi_manager.o(.data)
    g_rx_index                               0x20000006   Data           2  wifi_manager.o(.data)
    g_wifi_mutex                             0x20000008   Data           4  wifi_manager.o(.data)
    g_response_sem                           0x2000000c   Data           4  wifi_manager.o(.data)
    .data                                    0x20000010   Section      247  freertos.o(.data)
    sgp_counter                              0x2000001e   Data           1  freertos.o(.data)
    send_counter                             0x2000001f   Data           1  freertos.o(.data)
    tvoc                                     0x20000024   Data           2  freertos.o(.data)
    eco2                                     0x20000026   Data           2  freertos.o(.data)
    temperature                              0x20000068   Data           4  freertos.o(.data)
    humidity                                 0x2000006c   Data           4  freertos.o(.data)
    last_update                              0x20000070   Data           4  freertos.o(.data)
    last_beep_time                           0x20000074   Data           4  freertos.o(.data)
    .data                                    0x20000108   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000114   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000118   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x2000011c   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000120   Data           4  tasks.o(.data)
    xTickCount                               0x20000124   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000128   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x2000012c   Data           4  tasks.o(.data)
    xPendedTicks                             0x20000130   Data           4  tasks.o(.data)
    xYieldPending                            0x20000134   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000138   Data           4  tasks.o(.data)
    uxTaskNumber                             0x2000013c   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000140   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000144   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000148   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x2000014c   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000150   Data           4  tasks.o(.data)
    .data                                    0x20000154   Section       20  timers.o(.data)
    xTimerQueue                              0x20000154   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x20000158   Data           4  timers.o(.data)
    xLastTime                                0x2000015c   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x20000160   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000164   Data           4  timers.o(.data)
    .data                                    0x20000168   Section        4  cmsis_os2.o(.data)
    KernelState                              0x20000168   Data           4  cmsis_os2.o(.data)
    .data                                    0x2000016c   Section       32  heap_4.o(.data)
    pxEnd                                    0x2000016c   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000170   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000174   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000178   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x2000017c   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x20000180   Data           4  heap_4.o(.data)
    xStart                                   0x20000184   Data           8  heap_4.o(.data)
    .data                                    0x2000018c   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x2000018c   Data           1  port.o(.data)
    uxCriticalNesting                        0x20000190   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x20000194   Data           4  port.o(.data)
    .bss                                     0x20000198   Section     1153  oled.o(.bss)
    buffer                                   0x20000198   Data         129  oled.o(.bss)
    .bss                                     0x2000061c   Section      840  wifi_manager.o(.bss)
    g_wifi_connection                        0x2000061c   Data          72  wifi_manager.o(.bss)
    g_wifi_rx_buffer                         0x20000664   Data         512  wifi_manager.o(.bss)
    g_wifi_tx_buffer                         0x20000864   Data         256  wifi_manager.o(.bss)
    .bss                                     0x20000964   Section     1060  freertos.o(.bss)
    .bss                                     0x20000d88   Section      168  i2c.o(.bss)
    .bss                                     0x20000e30   Section       72  usart.o(.bss)
    .bss                                     0x20000e78   Section       72  stm32f1xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20000ec0   Section       64  queue.o(.bss)
    .bss                                     0x20000f00   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x20000f00   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20001360   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20001374   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20001388   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x2000139c   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x200013b0   Data          20  tasks.o(.bss)
    .bss                                     0x200013c4   Section      280  timers.o(.bss)
    xStaticTimerQueue                        0x200013c4   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x20001414   Data         160  timers.o(.bss)
    xActiveTimerList1                        0x200014b4   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x200014c8   Data          20  timers.o(.bss)
    .bss                                     0x200014dc   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x200014dc   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x20001538   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x20001738   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x20001794   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x20001b94   Section     4096  heap_4.o(.bss)
    ucHeap                                   0x20001b94   Data        4096  heap_4.o(.bss)
    .bss                                     0x20002b94   Section       96  libspace.o(.bss)
    HEAP                                     0x20002bf8   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20002bf8   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20002df8   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20002df8   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x200031f8   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x08000129   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x08000129   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000185   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001a1   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001a1   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x080001a7   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_c                                0x080001ad   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001b3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x080001b9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001bd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080001c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080001d1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001d3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001d5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001d5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001d5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001db   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001db   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001e7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001e9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001e9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x080001f5   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x08000219   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x08000239   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x08000285   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x0800028d   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x080002a7   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x080002a9   Thumb Code     0  startup_stm32f103xb.o(.text)
    __2snprintf                              0x080002cd   Thumb Code    50  __2snprintf.o(.text)
    _printf_pre_padding                      0x08000305   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000331   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000353   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080003a5   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x0800041d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strstr                                   0x080005a5   Thumb Code    36  strstr.o(.text)
    strcpy                                   0x080005c9   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x08000611   Thumb Code    62  strlen.o(.text)
    strcat                                   0x0800064f   Thumb Code    24  strcat.o(.text)
    __aeabi_memcpy                           0x08000667   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000667   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080006cd   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080006f1   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080006f1   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080006f1   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000739   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000755   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x08000765   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000765   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000769   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080007a9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080007a9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080007a9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080007ad   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080007f9   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000879   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800087b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800087d   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x0800087f   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000931   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000adf   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000d57   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000d7d   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000d87   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000d97   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000dab   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000dbb   Thumb Code     8  _printf_char.o(.text)
    __user_libspace                          0x08000dc5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000dc5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000dc5   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x08000dcd   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000dd5   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000e61   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000ee1   Thumb Code   216  bigflt0.o(.text)
    __user_setup_stackheap                   0x08000fbd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001007   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x08001015   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001021   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001021   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001023   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08001023   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001061   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080010a7   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001107   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800143f   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001505   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800152d   Thumb Code    40  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001555   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AHT10_GetTemperatureHumidity             0x08001799   Thumb Code   188  aht10.o(i.AHT10_GetTemperatureHumidity)
    AHT10_Init                               0x08001869   Thumb Code    54  aht10.o(i.AHT10_Init)
    AHT10_SimpleTest                         0x080018ad   Thumb Code    44  aht10.o(i.AHT10_SimpleTest)
    BusFault_Handler                         0x080018dd   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    Buzzer_Start_Alarm                       0x080018e1   Thumb Code    30  freertos.o(i.Buzzer_Start_Alarm)
    Buzzer_Start_Timer                       0x08001905   Thumb Code    32  freertos.o(i.Buzzer_Start_Timer)
    Buzzer_Stop                              0x08001929   Thumb Code    20  freertos.o(i.Buzzer_Stop)
    Buzzer_Update                            0x08001945   Thumb Code   138  freertos.o(i.Buzzer_Update)
    Check_Alarm_Repeat                       0x080019d9   Thumb Code    24  freertos.o(i.Check_Alarm_Repeat)
    Check_Multi_Alarms                       0x080019f1   Thumb Code   102  freertos.o(i.Check_Multi_Alarms)
    DS1302_BCD2DEC                           0x08001a5d   Thumb Code    18  ds1302.o(i.DS1302_BCD2DEC)
    DS1302_DAT_Input                         0x08001a71   Thumb Code    26  ds1302.o(i.DS1302_DAT_Input)
    DS1302_DAT_Output                        0x08001a91   Thumb Code    28  ds1302.o(i.DS1302_DAT_Output)
    DS1302_DEC2BCD                           0x08001ab1   Thumb Code    22  ds1302.o(i.DS1302_DEC2BCD)
    DS1302_GetTime                           0x08001add   Thumb Code   106  ds1302.o(i.DS1302_GetTime)
    DS1302_Init                              0x08001b49   Thumb Code   102  ds1302.o(i.DS1302_Init)
    DS1302_ReadReg                           0x08001bb5   Thumb Code   140  ds1302.o(i.DS1302_ReadReg)
    DS1302_SetTime                           0x08001c45   Thumb Code   194  ds1302.o(i.DS1302_SetTime)
    DS1302_WriteReg                          0x08001d5d   Thumb Code    82  ds1302.o(i.DS1302_WriteReg)
    DebugMon_Handler                         0x08001db5   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Decrease_Setting_Value                   0x08001db9   Thumb Code   358  freertos.o(i.Decrease_Setting_Value)
    Display_Alarm_Setting                    0x08001f29   Thumb Code   158  freertos.o(i.Display_Alarm_Setting)
    Display_Alarm_Simple                     0x08002031   Thumb Code   168  freertos.o(i.Display_Alarm_Simple)
    Display_Clock_Interface                  0x08002145   Thumb Code   420  freertos.o(i.Display_Clock_Interface)
    Display_Data_Log                         0x080022fd   Thumb Code   522  freertos.o(i.Display_Data_Log)
    Display_Multi_Alarm_Setting              0x08002655   Thumb Code   200  freertos.o(i.Display_Multi_Alarm_Setting)
    Display_Repeat_Pattern                   0x08002771   Thumb Code   138  freertos.o(i.Display_Repeat_Pattern)
    Display_Sensor_Interface                 0x08002831   Thumb Code   554  freertos.o(i.Display_Sensor_Interface)
    Display_Stopwatch_Interface              0x08002a91   Thumb Code   160  freertos.o(i.Display_Stopwatch_Interface)
    Display_Time_Setting                     0x08002b75   Thumb Code   212  freertos.o(i.Display_Time_Setting)
    Display_Timer_Interface                  0x08002ce1   Thumb Code   160  freertos.o(i.Display_Timer_Interface)
    Display_Timer_Setting                    0x08002dd5   Thumb Code   156  freertos.o(i.Display_Timer_Setting)
    Display_Weather_Interface                0x08002ef9   Thumb Code   298  freertos.o(i.Display_Weather_Interface)
    EXTI15_10_IRQHandler                     0x080030c9   Thumb Code    38  stm32f1xx_it.o(i.EXTI15_10_IRQHandler)
    Enter_Setting_Mode                       0x080030f1   Thumb Code   106  freertos.o(i.Enter_Setting_Mode)
    Error_Handler                            0x08003169   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x0800316d   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080031b5   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x0800324d   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08003271   Thumb Code    92  freertos.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080032d5   Thumb Code    18  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080032ed   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080034cd   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080034d7   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080034e1   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x080034ed   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_IsDeviceReady                    0x08003675   Thumb Code   354  stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    HAL_I2C_Master_Receive                   0x080037e1   Thumb Code   576  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x08003a31   Thumb Code   290  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08003b5d   Thumb Code   134  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08003bf5   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003c05   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003c29   Thumb Code   132  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003cc1   Thumb Code    62  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003d09   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003d25   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003d65   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08003d89   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08003eb5   Thumb Code    54  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08003ef5   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003f15   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003f35   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003f81   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_TIMEx_BreakCallback                  0x080042a1   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080042a3   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x080042a5   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080042ff   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08004301   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08004359   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x0800435b   Thumb Code   304  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x0800448b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800448d   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08004491   Thumb Code    14  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x080044a5   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x080044a7   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x080044a9   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080044ad   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004719   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x0800477d   Thumb Code   116  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080047fd   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004819   Thumb Code    52  wifi_manager.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x0800485d   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080048fd   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080048ff   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    Increase_Setting_Value                   0x08004d25   Thumb Code   362  freertos.o(i.Increase_Setting_Value)
    MX_FREERTOS_Init                         0x08004e99   Thumb Code   130  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x08004f39   Thumb Code   190  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x0800500d   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x0800504d   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_USART1_UART_Init                      0x0800508d   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x080050c5   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080050c7   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    Next_Setting_Item                        0x080050c9   Thumb Code    80  freertos.o(i.Next_Setting_Item)
    OLED_Clear                               0x0800511d   Thumb Code    34  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08005145   Thumb Code    86  oled.o(i.OLED_ClearArea)
    OLED_Init                                0x080051a1   Thumb Code   182  oled.o(i.OLED_Init)
    OLED_PowerOnInit                         0x08005257   Thumb Code    30  oled.o(i.OLED_PowerOnInit)
    OLED_SetCursor                           0x08005275   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08005299   Thumb Code    52  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x080052d5   Thumb Code   182  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08005391   Thumb Code    44  oled.o(i.OLED_ShowString)
    OLED_Update                              0x080053bd   Thumb Code    34  oled.o(i.OLED_Update)
    OLED_WriteCommand                        0x080053e5   Thumb Code    30  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08005409   Thumb Code    44  oled.o(i.OLED_WriteData)
    Record_Sensor_Data                       0x0800543d   Thumb Code   134  freertos.o(i.Record_Sensor_Data)
    SGP30_CRC8                               0x080054d1   Thumb Code    50  sgp30.o(i.SGP30_CRC8)
    SGP30_GetAirQuality                      0x08005505   Thumb Code   134  sgp30.o(i.SGP30_GetAirQuality)
    SGP30_Init                               0x08005599   Thumb Code    56  sgp30.o(i.SGP30_Init)
    SGP30_IsPresent                          0x080055dd   Thumb Code    24  sgp30.o(i.SGP30_IsPresent)
    SGP30_SetHumidity                        0x080055f9   Thumb Code    90  sgp30.o(i.SGP30_SetHumidity)
    Save_And_Exit_Setting                    0x08005661   Thumb Code   130  freertos.o(i.Save_And_Exit_Setting)
    StartDefaultTask                         0x080056ed   Thumb Code    12  freertos.o(i.StartDefaultTask)
    StartDisplayTask                         0x080056f9   Thumb Code   610  freertos.o(i.StartDisplayTask)
    StartKeyTask                             0x080059dd   Thumb Code   506  freertos.o(i.StartKeyTask)
    StartSensorTask                          0x08005be9   Thumb Code   350  freertos.o(i.StartSensorTask)
    StartWiFiTask                            0x08005d65   Thumb Code   170  freertos.o(i.StartWiFiTask)
    SysTick_Handler                          0x08005e21   Thumb Code    26  cmsis_os2.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005e3b   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08005e99   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM4_IRQHandler                          0x08005e9d   Thumb Code     6  stm32f1xx_it.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x08005ea9   Thumb Code   108  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    UART_Start_Receive_IT                    0x080060f9   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x080061a1   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    Update_Daily_Stats                       0x080061ad   Thumb Code   102  freertos.o(i.Update_Daily_Stats)
    Update_Timer                             0x0800621d   Thumb Code    70  freertos.o(i.Update_Timer)
    UsageFault_Handler                       0x0800626d   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    Weather_StatusToIcon                     0x0800626f   Thumb Code    44  weather_api.o(i.Weather_StatusToIcon)
    WiFi_CheckModule                         0x0800629d   Thumb Code    22  wifi_manager.o(i.WiFi_CheckModule)
    WiFi_Init                                0x080062d9   Thumb Code    72  wifi_manager.o(i.WiFi_Init)
    WiFi_SendATCommand                       0x0800632d   Thumb Code   148  wifi_manager.o(i.WiFi_SendATCommand)
    __ARM_fpclassify                         0x080063ed   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08006435   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x08006445   Thumb Code    82  main.o(i.main)
    osDelay                                  0x0800649d   Thumb Code    26  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x080064b9   Thumb Code    32  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x080064dd   Thumb Code    48  cmsis_os2.o(i.osKernelStart)
    osMessageQueueGet                        0x08006515   Thumb Code    96  cmsis_os2.o(i.osMessageQueueGet)
    osMessageQueueNew                        0x08006579   Thumb Code    88  cmsis_os2.o(i.osMessageQueueNew)
    osMessageQueuePut                        0x080065d1   Thumb Code   100  cmsis_os2.o(i.osMessageQueuePut)
    osMutexAcquire                           0x08006639   Thumb Code    82  cmsis_os2.o(i.osMutexAcquire)
    osMutexNew                               0x0800668b   Thumb Code   104  cmsis_os2.o(i.osMutexNew)
    osMutexRelease                           0x080066f3   Thumb Code    66  cmsis_os2.o(i.osMutexRelease)
    osSemaphoreNew                           0x08006735   Thumb Code   152  cmsis_os2.o(i.osSemaphoreNew)
    osThreadNew                              0x080067cd   Thumb Code   136  cmsis_os2.o(i.osThreadNew)
    pvPortMalloc                             0x08007001   Thumb Code   216  heap_4.o(i.pvPortMalloc)
    pvTaskIncrementMutexHeldCount            0x080070dd   Thumb Code    18  tasks.o(i.pvTaskIncrementMutexHeldCount)
    pxPortInitialiseStack                    0x080070f5   Thumb Code    32  port.o(i.pxPortInitialiseStack)
    uxListRemove                             0x08007119   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08007141   Thumb Code    16  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x08007155   Thumb Code    18  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x0800716d   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08007183   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08007189   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x080071b9   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x080071d1   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08007211   Thumb Code    34  port.o(i.vPortExitCritical)
    vPortFree                                0x08007239   Thumb Code    94  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x0800729d   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x080072c1   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x08007315   Thumb Code    34  queue.o(i.vQueueAddToRegistry)
    vQueueDelete                             0x0800733d   Thumb Code    46  queue.o(i.vQueueDelete)
    vQueueUnregisterQueue                    0x0800736d   Thumb Code    36  queue.o(i.vQueueUnregisterQueue)
    vQueueWaitForMessageRestricted           0x08007395   Thumb Code    68  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x080073d9   Thumb Code    66  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08007425   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08007435   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08007441   Thumb Code    44  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08007471   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskPriorityDisinheritAfterTimeout      0x080074a9   Thumb Code   134  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    vTaskStartScheduler                      0x08007539   Thumb Code   118  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x080075c1   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x080075d1   Thumb Code    90  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x08007635   Thumb Code   154  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x080076dd   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueCreateCountingSemaphore            0x08007709   Thumb Code    58  queue.o(i.xQueueCreateCountingSemaphore)
    xQueueCreateCountingSemaphoreStatic      0x08007743   Thumb Code    64  queue.o(i.xQueueCreateCountingSemaphoreStatic)
    xQueueCreateMutex                        0x08007783   Thumb Code    22  queue.o(i.xQueueCreateMutex)
    xQueueCreateMutexStatic                  0x08007799   Thumb Code    26  queue.o(i.xQueueCreateMutexStatic)
    xQueueGenericCreate                      0x080077b3   Thumb Code    66  queue.o(i.xQueueGenericCreate)
    xQueueGenericCreateStatic                0x080077f5   Thumb Code   102  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x0800785d   Thumb Code   132  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x080078e5   Thumb Code   346  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08007a45   Thumb Code   190  queue.o(i.xQueueGenericSendFromISR)
    xQueueGiveMutexRecursive                 0x08007b03   Thumb Code    62  queue.o(i.xQueueGiveMutexRecursive)
    xQueueReceive                            0x08007b41   Thumb Code   308  queue.o(i.xQueueReceive)
    xQueueReceiveFromISR                     0x08007c79   Thumb Code   154  queue.o(i.xQueueReceiveFromISR)
    xQueueSemaphoreTake                      0x08007d15   Thumb Code   372  queue.o(i.xQueueSemaphoreTake)
    xQueueTakeMutexRecursive                 0x08007e8d   Thumb Code    64  queue.o(i.xQueueTakeMutexRecursive)
    xTaskCheckForTimeOut                     0x08007ecd   Thumb Code   112  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08007f41   Thumb Code    90  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08007f9b   Thumb Code    86  tasks.o(i.xTaskCreateStatic)
    xTaskGetCurrentTaskHandle                0x08007ff1   Thumb Code     6  tasks.o(i.xTaskGetCurrentTaskHandle)
    xTaskGetSchedulerState                   0x08007ffd   Thumb Code    22  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08008019   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08008025   Thumb Code   192  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x080080ed   Thumb Code   118  tasks.o(i.xTaskPriorityDisinherit)
    xTaskPriorityInherit                     0x0800816d   Thumb Code   112  tasks.o(i.xTaskPriorityInherit)
    xTaskRemoveFromEventList                 0x080081e5   Thumb Code    98  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x08008255   Thumb Code   182  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08008319   Thumb Code    78  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08008379   Thumb Code    98  timers.o(i.xTimerGenericCommand)
    _get_lc_numeric                          0x080083e1   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x0800840d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800840d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcheck_NaN1                        0x08008471   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ui2d                             0x08008481   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08008481   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x080084a9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080084a9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080085fd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08008699   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x080086a5   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080086a5   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x080086fd   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080086fd   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x080087c1   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __aeabi_fdiv                             0x080087cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080087cd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x08008951   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08008951   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08008989   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08008989   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_ui2f                             0x080089c9   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x080089c9   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_fmul                             0x080089f1   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080089f1   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08008af3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08008b7f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x08008b89   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08008b89   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x08008c73   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x08008c77   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08008cdb   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __ARM_scalbnf                            0x08008d37   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    __fpl_cmpreturn                          0x08008d83   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    OLED_F8x16                               0x08008db2   Data        1520  oled_data.o(.constdata)
    __I$use$fp                               0x08008db2   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F6x8                                0x080093a2   Data         570  oled_data.o(.constdata)
    defaultTask_attributes                   0x080095e4   Data          36  freertos.o(.constdata)
    KeyTask_attributes                       0x08009608   Data          36  freertos.o(.constdata)
    SensorTask_attributes                    0x0800962c   Data          36  freertos.o(.constdata)
    DisplayTask_attributes                   0x08009650   Data          36  freertos.o(.constdata)
    WiFiTask_attributes                      0x08009674   Data          36  freertos.o(.constdata)
    KeyEventQueue_attributes                 0x08009698   Data          24  freertos.o(.constdata)
    SensorDataQueue_attributes               0x080096b0   Data          24  freertos.o(.constdata)
    DisplayMutex_attributes                  0x080096c8   Data          16  freertos.o(.constdata)
    TimeUpdateSem_attributes                 0x080096d8   Data          16  freertos.o(.constdata)
    AHBPrescTable                            0x0800975a   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x0800976a   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x080098ac   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080098cc   Number         0  anon$$obj.o(Region$$Table)
    aht10_calibrated                         0x20000000   Data           1  aht10.o(.data)
    sgp30_initialized                        0x20000001   Data           1  sgp30.o(.data)
    sgp30_enable                             0x20000010   Data           1  freertos.o(.data)
    g_display_mode                           0x20000011   Data           1  freertos.o(.data)
    g_data_view_mode                         0x20000012   Data           1  freertos.o(.data)
    g_display_updating                       0x20000013   Data           1  freertos.o(.data)
    g_force_display_update                   0x20000014   Data           1  freertos.o(.data)
    g_setting_mode                           0x20000015   Data           1  freertos.o(.data)
    g_setting_item                           0x20000016   Data           1  freertos.o(.data)
    g_key_interrupt_flag                     0x20000017   Data           1  freertos.o(.data)
    g_key_up_interrupt                       0x20000018   Data           1  freertos.o(.data)
    g_key_down_interrupt                     0x20000019   Data           1  freertos.o(.data)
    g_key_menu_interrupt                     0x2000001a   Data           1  freertos.o(.data)
    g_key_set_interrupt                      0x2000001b   Data           1  freertos.o(.data)
    g_fast_response_mode                     0x2000001c   Data           1  freertos.o(.data)
    g_weather_data_valid                     0x2000001d   Data           1  freertos.o(.data)
    g_editing_alarm_id                       0x20000020   Data           1  freertos.o(.data)
    g_current_co2                            0x20000022   Data           2  freertos.o(.data)
    g_sensor_display_timeout                 0x20000028   Data           4  freertos.o(.data)
    g_current_temperature                    0x2000002c   Data           4  freertos.o(.data)
    g_current_humidity                       0x20000030   Data           4  freertos.o(.data)
    g_system_watchdog                        0x20000034   Data           4  freertos.o(.data)
    g_setting_timeout                        0x20000038   Data           4  freertos.o(.data)
    g_key_press_time                         0x2000003c   Data           4  freertos.o(.data)
    g_debug_counter                          0x20000040   Data           4  freertos.o(.data)
    g_key_up_interrupt_time                  0x20000044   Data           4  freertos.o(.data)
    g_key_down_interrupt_time                0x20000048   Data           4  freertos.o(.data)
    g_key_menu_interrupt_time                0x2000004c   Data           4  freertos.o(.data)
    g_key_set_interrupt_time                 0x20000050   Data           4  freertos.o(.data)
    g_last_weather_update                    0x20000054   Data           4  freertos.o(.data)
    g_wifi_init_result                       0x20000058   Data           4  freertos.o(.data)
    g_wifi_connect_result                    0x2000005c   Data           4  freertos.o(.data)
    g_weather_get_result                     0x20000060   Data           4  freertos.o(.data)
    g_wifi_test_count                        0x20000064   Data           4  freertos.o(.data)
    defaultTaskHandle                        0x20000078   Data           4  freertos.o(.data)
    KeyTaskHandle                            0x2000007c   Data           4  freertos.o(.data)
    SensorTaskHandle                         0x20000080   Data           4  freertos.o(.data)
    DisplayTaskHandle                        0x20000084   Data           4  freertos.o(.data)
    WiFiTaskHandle                           0x20000088   Data           4  freertos.o(.data)
    KeyEventQueueHandle                      0x2000008c   Data           4  freertos.o(.data)
    SensorDataQueueHandle                    0x20000090   Data           4  freertos.o(.data)
    DisplayMutexHandle                       0x20000094   Data           4  freertos.o(.data)
    TimeUpdateSemHandle                      0x20000098   Data           4  freertos.o(.data)
    g_sgp_data                               0x2000009c   Data           6  freertos.o(.data)
    g_aht_data                               0x200000a4   Data           8  freertos.o(.data)
    g_current_time                           0x200000ac   Data           8  freertos.o(.data)
    g_daily_stats                            0x200000b4   Data           8  freertos.o(.data)
    g_temp_time                              0x200000bc   Data           8  freertos.o(.data)
    g_alarm_manager                          0x200000c4   Data          67  freertos.o(.data)
    uwTickFreq                               0x20000108   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x2000010c   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000110   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000114   Data           4  system_stm32f1xx.o(.data)
    pxCurrentTCB                             0x20000118   Data           4  tasks.o(.data)
    OLED_DisplayBuf                          0x20000219   Data        1024  oled.o(.bss)
    g_timer                                  0x20000964   Data          12  freertos.o(.bss)
    g_stopwatch                              0x20000970   Data          12  freertos.o(.bss)
    g_data_logger                            0x2000097c   Data         872  freertos.o(.bss)
    g_buzzer                                 0x20000ce4   Data          20  freertos.o(.bss)
    g_weather_data                           0x20000cf8   Data         104  freertos.o(.bss)
    g_temp_alarm                             0x20000d60   Data          13  freertos.o(.bss)
    g_temp_multi_alarm                       0x20000d6d   Data          13  freertos.o(.bss)
    g_temp_timer                             0x20000d7c   Data          12  freertos.o(.bss)
    hi2c1                                    0x20000d88   Data          84  i2c.o(.bss)
    hi2c2                                    0x20000ddc   Data          84  i2c.o(.bss)
    huart1                                   0x20000e30   Data          72  usart.o(.bss)
    htim4                                    0x20000e78   Data          72  stm32f1xx_hal_timebase_tim.o(.bss)
    xQueueRegistry                           0x20000ec0   Data          64  queue.o(.bss)
    __libspace_start                         0x20002b94   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20002bf4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009a80, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x0000991c])

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x000098e8, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x00000008   Code   RO         6224  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         6700    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000005a   Code   RO         6698    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000182   0x00000002   PAD
    0x08000184   0x0000001c   Code   RO         6702    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a0   0x00000000   Code   RO         6193    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a0   0x00000006   Code   RO         6192    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001a6   0x00000006   Code   RO         6191    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001ac   0x00000006   Code   RO         6189    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001b2   0x00000006   Code   RO         6190    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001b8   0x00000004   Code   RO         6371    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001bc   0x00000002   Code   RO         6531    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001be   0x00000000   Code   RO         6533    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001be   0x00000000   Code   RO         6535    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001be   0x00000000   Code   RO         6538    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001be   0x00000000   Code   RO         6540    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001be   0x00000000   Code   RO         6542    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001be   0x00000006   Code   RO         6543    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001c4   0x00000000   Code   RO         6545    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001c4   0x00000000   Code   RO         6547    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001c4   0x00000000   Code   RO         6549    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001c4   0x0000000a   Code   RO         6550    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6551    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6553    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6555    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6557    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6559    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6561    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6563    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6565    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6569    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6571    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6573    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001ce   0x00000000   Code   RO         6575    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001ce   0x00000002   Code   RO         6576    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001d0   0x00000002   Code   RO         6635    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001d2   0x00000000   Code   RO         6659    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001d2   0x00000000   Code   RO         6662    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001d2   0x00000000   Code   RO         6665    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001d2   0x00000000   Code   RO         6667    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001d2   0x00000000   Code   RO         6670    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001d2   0x00000002   Code   RO         6671    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001d4   0x00000000   Code   RO         6306    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001d4   0x00000000   Code   RO         6437    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001d4   0x00000006   Code   RO         6449    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001da   0x00000000   Code   RO         6439    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001da   0x00000004   Code   RO         6440    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001de   0x00000000   Code   RO         6442    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001de   0x00000008   Code   RO         6443    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001e6   0x00000002   Code   RO         6579    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001e8   0x00000000   Code   RO         6601    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001e8   0x00000004   Code   RO         6602    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001ec   0x00000006   Code   RO         6603    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001f2   0x00000002   PAD
    0x080001f4   0x00000096   Code   RO         6074    .emb_text           port.o
    0x0800028a   0x00000002   PAD
    0x0800028c   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x080002cc   0x00000038   Code   RO         6157    .text               c_w.l(__2snprintf.o)
    0x08000304   0x0000004e   Code   RO         6163    .text               c_w.l(_printf_pad.o)
    0x08000352   0x00000052   Code   RO         6165    .text               c_w.l(_printf_str.o)
    0x080003a4   0x00000078   Code   RO         6167    .text               c_w.l(_printf_dec.o)
    0x0800041c   0x00000188   Code   RO         6186    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080005a4   0x00000024   Code   RO         6198    .text               c_w.l(strstr.o)
    0x080005c8   0x00000048   Code   RO         6200    .text               c_w.l(strcpy.o)
    0x08000610   0x0000003e   Code   RO         6202    .text               c_w.l(strlen.o)
    0x0800064e   0x00000018   Code   RO         6204    .text               c_w.l(strcat.o)
    0x08000666   0x0000008a   Code   RO         6206    .text               c_w.l(rt_memcpy_v6.o)
    0x080006f0   0x00000064   Code   RO         6208    .text               c_w.l(rt_memcpy_w.o)
    0x08000754   0x00000010   Code   RO         6212    .text               c_w.l(aeabi_memset.o)
    0x08000764   0x00000044   Code   RO         6214    .text               c_w.l(rt_memclr.o)
    0x080007a8   0x0000004e   Code   RO         6216    .text               c_w.l(rt_memclr_w.o)
    0x080007f6   0x00000002   PAD
    0x080007f8   0x00000080   Code   RO         6220    .text               c_w.l(strcmpv7m.o)
    0x08000878   0x00000006   Code   RO         6222    .text               c_w.l(heapauxi.o)
    0x0800087e   0x000000b2   Code   RO         6316    .text               c_w.l(_printf_intcommon.o)
    0x08000930   0x0000041a   Code   RO         6320    .text               c_w.l(_printf_fp_dec.o)
    0x08000d4a   0x00000002   PAD
    0x08000d4c   0x00000030   Code   RO         6322    .text               c_w.l(_printf_char_common.o)
    0x08000d7c   0x0000000a   Code   RO         6324    .text               c_w.l(_sputc.o)
    0x08000d86   0x00000010   Code   RO         6326    .text               c_w.l(_snputc.o)
    0x08000d96   0x0000002c   Code   RO         6328    .text               c_w.l(_printf_char.o)
    0x08000dc2   0x00000002   PAD
    0x08000dc4   0x00000008   Code   RO         6433    .text               c_w.l(libspace.o)
    0x08000dcc   0x00000008   Code   RO         6456    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000dd4   0x0000008a   Code   RO         6458    .text               c_w.l(lludiv10.o)
    0x08000e5e   0x00000002   PAD
    0x08000e60   0x00000080   Code   RO         6465    .text               c_w.l(_printf_fp_infnan.o)
    0x08000ee0   0x000000dc   Code   RO         6475    .text               c_w.l(bigflt0.o)
    0x08000fbc   0x0000004a   Code   RO         6515    .text               c_w.l(sys_stackheap_outer.o)
    0x08001006   0x0000000c   Code   RO         6524    .text               c_w.l(exit.o)
    0x08001012   0x00000002   PAD
    0x08001014   0x0000000c   Code   RO         6623    .text               c_w.l(sys_exit.o)
    0x08001020   0x00000002   Code   RO         6648    .text               c_w.l(use_no_semi.o)
    0x08001022   0x00000000   Code   RO         6650    .text               c_w.l(indicate_semi.o)
    0x08001022   0x0000003e   Code   RO         6478    CL$$btod_d2e        c_w.l(btod.o)
    0x08001060   0x00000046   Code   RO         6480    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080010a6   0x00000060   Code   RO         6479    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001106   0x00000338   Code   RO         6488    CL$$btod_div_common  c_w.l(btod.o)
    0x0800143e   0x000000c6   Code   RO         6485    CL$$btod_e2e        c_w.l(btod.o)
    0x08001504   0x00000028   Code   RO         6482    CL$$btod_ediv       c_w.l(btod.o)
    0x0800152c   0x00000028   Code   RO         6481    CL$$btod_emul       c_w.l(btod.o)
    0x08001554   0x00000244   Code   RO         6487    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001798   0x000000d0   Code   RO           14    i.AHT10_GetTemperatureHumidity  aht10.o
    0x08001868   0x00000044   Code   RO           16    i.AHT10_Init        aht10.o
    0x080018ac   0x00000030   Code   RO           23    i.AHT10_SimpleTest  aht10.o
    0x080018dc   0x00000002   Code   RO         1433    i.BusFault_Handler  stm32f1xx_it.o
    0x080018de   0x00000002   PAD
    0x080018e0   0x00000024   Code   RO         1052    i.Buzzer_Start_Alarm  freertos.o
    0x08001904   0x00000024   Code   RO         1053    i.Buzzer_Start_Timer  freertos.o
    0x08001928   0x0000001c   Code   RO         1054    i.Buzzer_Stop       freertos.o
    0x08001944   0x00000094   Code   RO         1055    i.Buzzer_Update     freertos.o
    0x080019d8   0x00000018   Code   RO         1057    i.Check_Alarm_Repeat  freertos.o
    0x080019f0   0x0000006c   Code   RO         1058    i.Check_Multi_Alarms  freertos.o
    0x08001a5c   0x00000012   Code   RO          483    i.DS1302_BCD2DEC    ds1302.o
    0x08001a6e   0x00000002   PAD
    0x08001a70   0x00000020   Code   RO          484    i.DS1302_DAT_Input  ds1302.o
    0x08001a90   0x00000020   Code   RO          485    i.DS1302_DAT_Output  ds1302.o
    0x08001ab0   0x00000016   Code   RO          486    i.DS1302_DEC2BCD    ds1302.o
    0x08001ac6   0x00000016   Code   RO          487    i.DS1302_Delay_us   ds1302.o
    0x08001adc   0x0000006a   Code   RO          488    i.DS1302_GetTime    ds1302.o
    0x08001b46   0x00000002   PAD
    0x08001b48   0x0000006c   Code   RO          489    i.DS1302_Init       ds1302.o
    0x08001bb4   0x00000090   Code   RO          492    i.DS1302_ReadReg    ds1302.o
    0x08001c44   0x000000c2   Code   RO          493    i.DS1302_SetTime    ds1302.o
    0x08001d06   0x00000002   PAD
    0x08001d08   0x00000054   Code   RO          494    i.DS1302_WriteByte  ds1302.o
    0x08001d5c   0x00000058   Code   RO          495    i.DS1302_WriteReg   ds1302.o
    0x08001db4   0x00000002   Code   RO         1434    i.DebugMon_Handler  stm32f1xx_it.o
    0x08001db6   0x00000002   PAD
    0x08001db8   0x00000170   Code   RO         1059    i.Decrease_Setting_Value  freertos.o
    0x08001f28   0x00000108   Code   RO         1062    i.Display_Alarm_Setting  freertos.o
    0x08002030   0x00000114   Code   RO         1063    i.Display_Alarm_Simple  freertos.o
    0x08002144   0x000001b8   Code   RO         1064    i.Display_Clock_Interface  freertos.o
    0x080022fc   0x00000358   Code   RO         1065    i.Display_Data_Log  freertos.o
    0x08002654   0x0000011c   Code   RO         1066    i.Display_Multi_Alarm_Setting  freertos.o
    0x08002770   0x000000c0   Code   RO         1067    i.Display_Repeat_Pattern  freertos.o
    0x08002830   0x00000260   Code   RO         1068    i.Display_Sensor_Interface  freertos.o
    0x08002a90   0x000000e4   Code   RO         1069    i.Display_Stopwatch_Interface  freertos.o
    0x08002b74   0x0000016c   Code   RO         1070    i.Display_Time_Setting  freertos.o
    0x08002ce0   0x000000f4   Code   RO         1071    i.Display_Timer_Interface  freertos.o
    0x08002dd4   0x00000124   Code   RO         1072    i.Display_Timer_Setting  freertos.o
    0x08002ef8   0x000001d0   Code   RO         1073    i.Display_Weather_Interface  freertos.o
    0x080030c8   0x00000026   Code   RO         1435    i.EXTI15_10_IRQHandler  stm32f1xx_it.o
    0x080030ee   0x00000002   PAD
    0x080030f0   0x00000078   Code   RO         1074    i.Enter_Setting_Mode  freertos.o
    0x08003168   0x00000004   Code   RO          971    i.Error_Handler     main.o
    0x0800316c   0x00000046   Code   RO         2962    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x080031b2   0x00000002   PAD
    0x080031b4   0x00000098   Code   RO         2963    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x0800324c   0x00000024   Code   RO         2588    i.HAL_Delay         stm32f1xx_hal.o
    0x08003270   0x00000064   Code   RO         1076    i.HAL_GPIO_EXTI_Callback  freertos.o
    0x080032d4   0x00000018   Code   RO         2897    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x080032ec   0x000001e0   Code   RO         2898    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080034cc   0x0000000a   Code   RO         2900    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x080034d6   0x0000000a   Code   RO         2902    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080034e0   0x0000000c   Code   RO         2592    i.HAL_GetTick       stm32f1xx_hal.o
    0x080034ec   0x00000188   Code   RO         3569    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x08003674   0x0000016c   Code   RO         3570    i.HAL_I2C_IsDeviceReady  stm32f1xx_hal_i2c.o
    0x080037e0   0x00000250   Code   RO         3575    i.HAL_I2C_Master_Receive  stm32f1xx_hal_i2c.o
    0x08003a30   0x0000012c   Code   RO         3582    i.HAL_I2C_Master_Transmit  stm32f1xx_hal_i2c.o
    0x08003b5c   0x00000098   Code   RO         1344    i.HAL_I2C_MspInit   i2c.o
    0x08003bf4   0x00000010   Code   RO         2598    i.HAL_IncTick       stm32f1xx_hal.o
    0x08003c04   0x00000024   Code   RO         2599    i.HAL_Init          stm32f1xx_hal.o
    0x08003c28   0x00000098   Code   RO         1533    i.HAL_InitTick      stm32f1xx_hal_timebase_tim.o
    0x08003cc0   0x00000048   Code   RO         1509    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08003d08   0x0000001a   Code   RO         3058    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08003d22   0x00000002   PAD
    0x08003d24   0x00000040   Code   RO         3064    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08003d64   0x00000024   Code   RO         3065    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08003d88   0x0000012c   Code   RO         2756    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08003eb4   0x00000040   Code   RO         2760    i.HAL_RCC_GetClockConfig  stm32f1xx_hal_rcc.o
    0x08003ef4   0x00000020   Code   RO         2763    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08003f14   0x00000020   Code   RO         2764    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08003f34   0x0000004c   Code   RO         2765    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08003f80   0x00000320   Code   RO         2768    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x080042a0   0x00000002   Code   RO         2314    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x080042a2   0x00000002   Code   RO         2315    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x080042a4   0x0000005a   Code   RO         1610    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x080042fe   0x00000002   Code   RO         1612    i.HAL_TIM_Base_MspInit  stm32f1xx_hal_tim.o
    0x08004300   0x00000058   Code   RO         1615    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08004358   0x00000002   Code   RO         1644    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x0800435a   0x00000130   Code   RO         1658    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x0800448a   0x00000002   Code   RO         1661    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x0800448c   0x00000002   Code   RO         1688    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x0800448e   0x00000002   PAD
    0x08004490   0x00000014   Code   RO          972    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x080044a4   0x00000002   Code   RO         1701    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x080044a6   0x00000002   Code   RO         4007    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x080044a8   0x00000002   Code   RO         4021    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x080044aa   0x00000002   PAD
    0x080044ac   0x0000026c   Code   RO         4024    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08004718   0x00000064   Code   RO         4025    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x0800477c   0x00000080   Code   RO         1392    i.HAL_UART_MspInit  usart.o
    0x080047fc   0x0000001c   Code   RO         4030    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x08004818   0x00000044   Code   RO          800    i.HAL_UART_RxCpltCallback  wifi_manager.o
    0x0800485c   0x000000a0   Code   RO         4033    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x080048fc   0x00000002   Code   RO         4036    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x080048fe   0x00000002   Code   RO         1436    i.HardFault_Handler  stm32f1xx_it.o
    0x08004900   0x0000002e   Code   RO         3612    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x0800492e   0x00000002   PAD
    0x08004930   0x000000ec   Code   RO         3615    i.I2C_MasterRequestRead  stm32f1xx_hal_i2c.o
    0x08004a1c   0x0000009c   Code   RO         3616    i.I2C_MasterRequestWrite  stm32f1xx_hal_i2c.o
    0x08004ab8   0x00000056   Code   RO         3627    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08004b0e   0x00000002   PAD
    0x08004b10   0x00000090   Code   RO         3628    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08004ba0   0x000000bc   Code   RO         3629    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08004c5c   0x00000070   Code   RO         3630    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08004ccc   0x00000056   Code   RO         3631    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08004d22   0x00000002   PAD
    0x08004d24   0x00000174   Code   RO         1078    i.Increase_Setting_Value  freertos.o
    0x08004e98   0x000000a0   Code   RO         1079    i.MX_FREERTOS_Init  freertos.o
    0x08004f38   0x000000d4   Code   RO         1026    i.MX_GPIO_Init      gpio.o
    0x0800500c   0x00000040   Code   RO         1345    i.MX_I2C1_Init      i2c.o
    0x0800504c   0x00000040   Code   RO         1346    i.MX_I2C2_Init      i2c.o
    0x0800508c   0x00000038   Code   RO         1393    i.MX_USART1_UART_Init  usart.o
    0x080050c4   0x00000002   Code   RO         1437    i.MemManage_Handler  stm32f1xx_it.o
    0x080050c6   0x00000002   Code   RO         1438    i.NMI_Handler       stm32f1xx_it.o
    0x080050c8   0x00000054   Code   RO         1080    i.Next_Setting_Item  freertos.o
    0x0800511c   0x00000028   Code   RO          212    i.OLED_Clear        oled.o
    0x08005144   0x0000005c   Code   RO          213    i.OLED_ClearArea    oled.o
    0x080051a0   0x000000b6   Code   RO          225    i.OLED_Init         oled.o
    0x08005256   0x0000001e   Code   RO          228    i.OLED_PowerOnInit  oled.o
    0x08005274   0x00000022   Code   RO          232    i.OLED_SetCursor    oled.o
    0x08005296   0x00000002   PAD
    0x08005298   0x0000003c   Code   RO          234    i.OLED_ShowChar     oled.o
    0x080052d4   0x000000bc   Code   RO          237    i.OLED_ShowImage    oled.o
    0x08005390   0x0000002c   Code   RO          240    i.OLED_ShowString   oled.o
    0x080053bc   0x00000028   Code   RO          241    i.OLED_Update       oled.o
    0x080053e4   0x00000024   Code   RO          245    i.OLED_WriteCommand  oled.o
    0x08005408   0x00000034   Code   RO          246    i.OLED_WriteData    oled.o
    0x0800543c   0x00000094   Code   RO         1081    i.Record_Sensor_Data  freertos.o
    0x080054d0   0x00000032   Code   RO          577    i.SGP30_CRC8        sgp30.o
    0x08005502   0x00000002   PAD
    0x08005504   0x00000094   Code   RO          578    i.SGP30_GetAirQuality  sgp30.o
    0x08005598   0x00000044   Code   RO          580    i.SGP30_Init        sgp30.o
    0x080055dc   0x0000001c   Code   RO          581    i.SGP30_IsPresent   sgp30.o
    0x080055f8   0x00000068   Code   RO          583    i.SGP30_SetHumidity  sgp30.o
    0x08005660   0x0000008c   Code   RO         1083    i.Save_And_Exit_Setting  freertos.o
    0x080056ec   0x0000000c   Code   RO         1084    i.StartDefaultTask  freertos.o
    0x080056f8   0x000002e4   Code   RO         1085    i.StartDisplayTask  freertos.o
    0x080059dc   0x0000020c   Code   RO         1086    i.StartKeyTask      freertos.o
    0x08005be8   0x0000017c   Code   RO         1087    i.StartSensorTask   freertos.o
    0x08005d64   0x000000bc   Code   RO         1088    i.StartWiFiTask     freertos.o
    0x08005e20   0x0000001a   Code   RO         5506    i.SysTick_Handler   cmsis_os2.o
    0x08005e3a   0x0000005e   Code   RO          973    i.SystemClock_Config  main.o
    0x08005e98   0x00000002   Code   RO         4359    i.SystemInit        system_stm32f1xx.o
    0x08005e9a   0x00000002   PAD
    0x08005e9c   0x0000000c   Code   RO         1439    i.TIM4_IRQHandler   stm32f1xx_it.o
    0x08005ea8   0x00000078   Code   RO         1703    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08005f20   0x00000010   Code   RO         4038    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08005f30   0x0000004e   Code   RO         4048    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08005f7e   0x000000c2   Code   RO         4050    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08006040   0x000000b8   Code   RO         4051    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x080060f8   0x00000036   Code   RO         4053    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x0800612e   0x00000072   Code   RO         4054    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x080061a0   0x0000000c   Code   RO         1440    i.USART1_IRQHandler  stm32f1xx_it.o
    0x080061ac   0x00000070   Code   RO         1089    i.Update_Daily_Stats  freertos.o
    0x0800621c   0x00000050   Code   RO         1091    i.Update_Timer      freertos.o
    0x0800626c   0x00000002   Code   RO         1441    i.UsageFault_Handler  stm32f1xx_it.o
    0x0800626e   0x0000002c   Code   RO          655    i.Weather_StatusToIcon  weather_api.o
    0x0800629a   0x00000002   PAD
    0x0800629c   0x0000001c   Code   RO          801    i.WiFi_CheckModule  wifi_manager.o
    0x080062b8   0x00000020   Code   RO          802    i.WiFi_ClearRxBuffer  wifi_manager.o
    0x080062d8   0x00000054   Code   RO          806    i.WiFi_Init         wifi_manager.o
    0x0800632c   0x000000c0   Code   RO          808    i.WiFi_SendATCommand  wifi_manager.o
    0x080063ec   0x00000028   Code   RO         6511    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08006414   0x00000020   Code   RO         3071    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08006434   0x0000000e   Code   RO         6179    i._is_digit         c_w.l(__printf_wp.o)
    0x08006442   0x00000002   PAD
    0x08006444   0x00000058   Code   RO          975    i.main              main.o
    0x0800649c   0x0000001a   Code   RO         5508    i.osDelay           cmsis_os2.o
    0x080064b6   0x00000002   PAD
    0x080064b8   0x00000024   Code   RO         5522    i.osKernelInitialize  cmsis_os2.o
    0x080064dc   0x00000038   Code   RO         5525    i.osKernelStart     cmsis_os2.o
    0x08006514   0x00000064   Code   RO         5537    i.osMessageQueueGet  cmsis_os2.o
    0x08006578   0x00000058   Code   RO         5542    i.osMessageQueueNew  cmsis_os2.o
    0x080065d0   0x00000068   Code   RO         5543    i.osMessageQueuePut  cmsis_os2.o
    0x08006638   0x00000052   Code   RO         5545    i.osMutexAcquire    cmsis_os2.o
    0x0800668a   0x00000068   Code   RO         5548    i.osMutexNew        cmsis_os2.o
    0x080066f2   0x00000042   Code   RO         5549    i.osMutexRelease    cmsis_os2.o
    0x08006734   0x00000098   Code   RO         5553    i.osSemaphoreNew    cmsis_os2.o
    0x080067cc   0x00000088   Code   RO         5567    i.osThreadNew       cmsis_os2.o
    0x08006854   0x0000005c   Code   RO         4967    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x080068b0   0x000000d0   Code   RO         4968    i.prvAddNewTaskToReadyList  tasks.o
    0x08006980   0x00000058   Code   RO         5332    i.prvCheckForValidListAndQueue  timers.o
    0x080069d8   0x00000026   Code   RO         4564    i.prvCopyDataFromQueue  queue.o
    0x080069fe   0x0000006c   Code   RO         4565    i.prvCopyDataToQueue  queue.o
    0x08006a6a   0x00000034   Code   RO         4969    i.prvDeleteTCB      tasks.o
    0x08006a9e   0x00000002   PAD
    0x08006aa0   0x0000004c   Code   RO         6019    i.prvHeapInit       heap_4.o
    0x08006aec   0x00000060   Code   RO         4970    i.prvIdleTask       tasks.o
    0x08006b4c   0x00000016   Code   RO         4566    i.prvInitialiseMutex  queue.o
    0x08006b62   0x00000002   PAD
    0x08006b64   0x00000022   Code   RO         4567    i.prvInitialiseNewQueue  queue.o
    0x08006b86   0x00000002   PAD
    0x08006b88   0x000000b0   Code   RO         4971    i.prvInitialiseNewTask  tasks.o
    0x08006c38   0x0000004c   Code   RO         6020    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08006c84   0x00000038   Code   RO         5334    i.prvInsertTimerInActiveList  timers.o
    0x08006cbc   0x0000001c   Code   RO         4568    i.prvIsQueueEmpty   queue.o
    0x08006cd8   0x000000fc   Code   RO         5335    i.prvProcessReceivedCommands  timers.o
    0x08006dd4   0x000000c0   Code   RO         5336    i.prvProcessTimerOrBlockTask  timers.o
    0x08006e94   0x00000020   Code   RO         4973    i.prvResetNextTaskUnblockTime  tasks.o
    0x08006eb4   0x00000028   Code   RO         5337    i.prvSampleTimeNow  timers.o
    0x08006edc   0x0000006c   Code   RO         5338    i.prvSwitchTimerLists  timers.o
    0x08006f48   0x00000028   Code   RO         6075    i.prvTaskExitError  port.o
    0x08006f70   0x00000024   Code   RO         5339    i.prvTimerTask      timers.o
    0x08006f94   0x0000006a   Code   RO         4569    i.prvUnlockQueue    queue.o
    0x08006ffe   0x00000002   PAD
    0x08007000   0x000000dc   Code   RO         6021    i.pvPortMalloc      heap_4.o
    0x080070dc   0x00000018   Code   RO         4976    i.pvTaskIncrementMutexHeldCount  tasks.o
    0x080070f4   0x00000024   Code   RO         6076    i.pxPortInitialiseStack  port.o
    0x08007118   0x00000026   Code   RO         4524    i.uxListRemove      list.o
    0x0800713e   0x00000002   PAD
    0x08007140   0x00000014   Code   RO         5579    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x08007154   0x00000018   Code   RO         5580    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x0800716c   0x00000016   Code   RO         4525    i.vListInitialise   list.o
    0x08007182   0x00000006   Code   RO         4526    i.vListInitialiseItem  list.o
    0x08007188   0x00000030   Code   RO         4527    i.vListInsert       list.o
    0x080071b8   0x00000018   Code   RO         4528    i.vListInsertEnd    list.o
    0x080071d0   0x00000040   Code   RO         6078    i.vPortEnterCritical  port.o
    0x08007210   0x00000028   Code   RO         6079    i.vPortExitCritical  port.o
    0x08007238   0x00000064   Code   RO         6022    i.vPortFree         heap_4.o
    0x0800729c   0x00000024   Code   RO         6080    i.vPortSetupTimerInterrupt  port.o
    0x080072c0   0x00000054   Code   RO         6081    i.vPortValidateInterruptPriority  port.o
    0x08007314   0x00000028   Code   RO         4575    i.vQueueAddToRegistry  queue.o
    0x0800733c   0x0000002e   Code   RO         4576    i.vQueueDelete      queue.o
    0x0800736a   0x00000002   PAD
    0x0800736c   0x00000028   Code   RO         4578    i.vQueueUnregisterQueue  queue.o
    0x08007394   0x00000044   Code   RO         4579    i.vQueueWaitForMessageRestricted  queue.o
    0x080073d8   0x0000004c   Code   RO         4986    i.vTaskDelay        tasks.o
    0x08007424   0x00000010   Code   RO         4991    i.vTaskInternalSetTimeOutState  tasks.o
    0x08007434   0x0000000c   Code   RO         4992    i.vTaskMissedYield  tasks.o
    0x08007440   0x00000030   Code   RO         4994    i.vTaskPlaceOnEventList  tasks.o
    0x08007470   0x00000038   Code   RO         4995    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x080074a8   0x00000090   Code   RO         4997    i.vTaskPriorityDisinheritAfterTimeout  tasks.o
    0x08007538   0x00000088   Code   RO         5003    i.vTaskStartScheduler  tasks.o
    0x080075c0   0x00000010   Code   RO         5005    i.vTaskSuspendAll   tasks.o
    0x080075d0   0x00000064   Code   RO         5006    i.vTaskSwitchContext  tasks.o
    0x08007634   0x000000a8   Code   RO         6082    i.xPortStartScheduler  port.o
    0x080076dc   0x0000002c   Code   RO         6083    i.xPortSysTickHandler  port.o
    0x08007708   0x0000003a   Code   RO         4580    i.xQueueCreateCountingSemaphore  queue.o
    0x08007742   0x00000040   Code   RO         4581    i.xQueueCreateCountingSemaphoreStatic  queue.o
    0x08007782   0x00000016   Code   RO         4582    i.xQueueCreateMutex  queue.o
    0x08007798   0x0000001a   Code   RO         4583    i.xQueueCreateMutexStatic  queue.o
    0x080077b2   0x00000042   Code   RO         4584    i.xQueueGenericCreate  queue.o
    0x080077f4   0x00000066   Code   RO         4585    i.xQueueGenericCreateStatic  queue.o
    0x0800785a   0x00000002   PAD
    0x0800785c   0x00000088   Code   RO         4586    i.xQueueGenericReset  queue.o
    0x080078e4   0x00000160   Code   RO         4587    i.xQueueGenericSend  queue.o
    0x08007a44   0x000000be   Code   RO         4588    i.xQueueGenericSendFromISR  queue.o
    0x08007b02   0x0000003e   Code   RO         4592    i.xQueueGiveMutexRecursive  queue.o
    0x08007b40   0x00000138   Code   RO         4597    i.xQueueReceive     queue.o
    0x08007c78   0x0000009a   Code   RO         4598    i.xQueueReceiveFromISR  queue.o
    0x08007d12   0x00000002   PAD
    0x08007d14   0x00000178   Code   RO         4599    i.xQueueSemaphoreTake  queue.o
    0x08007e8c   0x00000040   Code   RO         4600    i.xQueueTakeMutexRecursive  queue.o
    0x08007ecc   0x00000074   Code   RO         5008    i.xTaskCheckForTimeOut  tasks.o
    0x08007f40   0x0000005a   Code   RO         5009    i.xTaskCreate       tasks.o
    0x08007f9a   0x00000056   Code   RO         5010    i.xTaskCreateStatic  tasks.o
    0x08007ff0   0x0000000c   Code   RO         5013    i.xTaskGetCurrentTaskHandle  tasks.o
    0x08007ffc   0x0000001c   Code   RO         5014    i.xTaskGetSchedulerState  tasks.o
    0x08008018   0x0000000c   Code   RO         5015    i.xTaskGetTickCount  tasks.o
    0x08008024   0x000000c8   Code   RO         5017    i.xTaskIncrementTick  tasks.o
    0x080080ec   0x00000080   Code   RO         5020    i.xTaskPriorityDisinherit  tasks.o
    0x0800816c   0x00000078   Code   RO         5021    i.xTaskPriorityInherit  tasks.o
    0x080081e4   0x00000070   Code   RO         5022    i.xTaskRemoveFromEventList  tasks.o
    0x08008254   0x000000c4   Code   RO         5023    i.xTaskResumeAll    tasks.o
    0x08008318   0x00000060   Code   RO         5348    i.xTimerCreateTimerTask  timers.o
    0x08008378   0x00000068   Code   RO         5349    i.xTimerGenericCommand  timers.o
    0x080083e0   0x0000002c   Code   RO         6503    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800840c   0x00000062   Code   RO         6226    x$fpl$d2f           fz_ws.l(d2f.o)
    0x0800846e   0x00000002   PAD
    0x08008470   0x00000010   Code   RO         6385    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08008480   0x00000026   Code   RO         6246    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080084a6   0x00000002   PAD
    0x080084a8   0x00000154   Code   RO         6254    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080085fc   0x0000009c   Code   RO         6389    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08008698   0x0000000c   Code   RO         6391    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080086a4   0x00000056   Code   RO         6258    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080086fa   0x00000002   PAD
    0x080086fc   0x000000c4   Code   RO         6260    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080087c0   0x0000000c   Code   RO         6395    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x080087cc   0x00000184   Code   RO         6267    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08008950   0x00000036   Code   RO         6270    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08008986   0x00000002   PAD
    0x08008988   0x0000003e   Code   RO         6274    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x080089c6   0x00000002   PAD
    0x080089c8   0x00000026   Code   RO         6278    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x080089ee   0x00000002   PAD
    0x080089f0   0x00000102   Code   RO         6284    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08008af2   0x0000008c   Code   RO         6397    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08008b7e   0x0000000a   Code   RO         6399    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08008b88   0x000000ea   Code   RO         6262    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08008c72   0x00000004   Code   RO         6286    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08008c76   0x00000064   Code   RO         6507    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08008cda   0x0000005c   Code   RO         6288    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08008d36   0x0000004c   Code   RO         6290    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x08008d82   0x00000030   Code   RO         6577    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08008db2   0x00000000   Code   RO         6407    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08008db2   0x000005f0   Data   RO          459    .constdata          oled_data.o
    0x080093a2   0x0000023a   Data   RO          460    .constdata          oled_data.o
    0x080095dc   0x00000008   Data   RO          976    .constdata          main.o
    0x080095e4   0x00000164   Data   RO         1093    .constdata          freertos.o
    0x08009748   0x00000012   Data   RO         2769    .constdata          stm32f1xx_hal_rcc.o
    0x0800975a   0x00000010   Data   RO         4360    .constdata          system_stm32f1xx.o
    0x0800976a   0x00000008   Data   RO         4361    .constdata          system_stm32f1xx.o
    0x08009772   0x00000011   Data   RO         6187    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08009783   0x00000001   PAD
    0x08009784   0x00000094   Data   RO         6476    .constdata          c_w.l(bigflt0.o)
    0x08009818   0x00000091   Data   RO         1094    .conststring        freertos.o
    0x080098a9   0x00000003   PAD
    0x080098ac   0x00000020   Data   RO         6696    Region$$Table       anon$$obj.o
    0x080098cc   0x0000001c   Data   RO         6502    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x000031f8, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x00000034])

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000001   Data   RW           26    .data               aht10.o
    0x20000001   0x00000001   Data   RW          584    .data               sgp30.o
    0x20000002   0x00000002   PAD
    0x20000004   0x0000000c   Data   RW          812    .data               wifi_manager.o
    0x20000010   0x000000f7   Data   RW         1095    .data               freertos.o
    0x20000107   0x00000001   PAD
    0x20000108   0x0000000c   Data   RW         2606    .data               stm32f1xx_hal.o
    0x20000114   0x00000004   Data   RW         4362    .data               system_stm32f1xx.o
    0x20000118   0x0000003c   Data   RW         5026    .data               tasks.o
    0x20000154   0x00000014   Data   RW         5357    .data               timers.o
    0x20000168   0x00000004   Data   RW         5582    .data               cmsis_os2.o
    0x2000016c   0x00000020   Data   RW         6028    .data               heap_4.o
    0x2000018c   0x0000000c   Data   RW         6084    .data               port.o
    0x20000198   0x00000481   Zero   RW          248    .bss                oled.o
    0x20000619   0x00000003   PAD
    0x2000061c   0x00000348   Zero   RW          811    .bss                wifi_manager.o
    0x20000964   0x00000424   Zero   RW         1092    .bss                freertos.o
    0x20000d88   0x000000a8   Zero   RW         1347    .bss                i2c.o
    0x20000e30   0x00000048   Zero   RW         1394    .bss                usart.o
    0x20000e78   0x00000048   Zero   RW         1536    .bss                stm32f1xx_hal_timebase_tim.o
    0x20000ec0   0x00000040   Zero   RW         4601    .bss                queue.o
    0x20000f00   0x000004c4   Zero   RW         5025    .bss                tasks.o
    0x200013c4   0x00000118   Zero   RW         5356    .bss                timers.o
    0x200014dc   0x000006b8   Zero   RW         5581    .bss                cmsis_os2.o
    0x20001b94   0x00001000   Zero   RW         6027    .bss                heap_4.o
    0x20002b94   0x00000060   Zero   RW         6434    .bss                c_w.l(libspace.o)
    0x20002bf4   0x00000004   PAD
    0x20002bf8   0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20002df8   0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       324         38          0          1          0     437123   aht10.o
      1020         30          0          4       1720      63383   cmsis_os2.o
       850         30          0          0          0       7586   ds1302.o
         0          0          0          0          0      19492   event_groups.o
      8420       1734        501        247       1060      32973   freertos.o
       212         22          0          0          0        939   gpio.o
       472         24          0         32       4096       4379   heap_4.o
       280         46          0          0        168       2134   i2c.o
       138          0          0          0          0       3062   list.o
       206         12          8          0          0       2464   main.o
       798         46          0          0       1153       6419   oled.o
         0          0       2090          0          0        934   oled_data.o
       662         70          0         12          0      10381   port.o
      2514         28          0          0         64      24712   queue.o
       398         44          0          1          0       4678   sgp30.o
        64         26        236          0       1536        788   startup_stm32f103xb.o
       100         18          0         12          0       5046   stm32f1xx_hal.o
       158         14          0          0          0      27734   stm32f1xx_hal_cortex.o
       222          4          0          0          0       1615   stm32f1xx_hal_dma.o
       524         40          0          0          0       3594   stm32f1xx_hal_gpio.o
      2702         64          0          0          0      11618   stm32f1xx_hal_i2c.o
        72         10          0          0          0        826   stm32f1xx_hal_msp.o
      1304         94         18          0          0       5273   stm32f1xx_hal_rcc.o
       612         24          0          0          0       6748   stm32f1xx_hal_tim.o
         4          0          0          0          0       1509   stm32f1xx_hal_tim_ex.o
       152         20          0          0         72       1355   stm32f1xx_hal_timebase_tim.o
      1554         10          0          0          0       9544   stm32f1xx_hal_uart.o
        74         12          0          0          0       3742   stm32f1xx_it.o
         0          0          0          0          0        388   stream_buffer.o
         2          0         24          4          0       1047   system_stm32f1xx.o
      2384        198          0         60       1220      26725   tasks.o
       972         80          0         20        280      30951   timers.o
       184         20          0          0         72       1566   usart.o
        44          8          0          0          0      14195   weather_api.o
       404         88          0         12        840       3707   wifi_manager.o

    ----------------------------------------------------------------------
     27880       <USER>       <GROUP>        408      12284     778630   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        54          0          3          3          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56          6          0          0          0         88   __2snprintf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1050          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
       220          4        148          0          0         96   bigflt0.o
      1910        128          0          0          0        672   btod.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        24          0          0          0          0         68   strcat.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        36          0          0          0          0         80   strstr.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
        16          4          0          0          0         68   dcheck1.o
        38          0          0          0          0         68   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        38          0          0          0          0         68   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        76          0          0          0          0         68   scalbnf.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      8158        <USER>        <GROUP>          0        100       5348   Library Totals
        28          0          1          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5632        210        193          0         96       3560   c_w.l
      2458        132          0          0          0       1720   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      8158        <USER>        <GROUP>          0        100       5348   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36038       3196       3106        408      12384     768582   Grand Totals
     36038       3196       3106         52      12384     768582   ELF Image Totals (compressed)
     36038       3196       3106         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                39144 (  38.23kB)
    Total RW  Size (RW Data + ZI Data)             12792 (  12.49kB)
    Total ROM Size (Code + RO Data + RW Data)      39196 (  38.28kB)

==============================================================================

