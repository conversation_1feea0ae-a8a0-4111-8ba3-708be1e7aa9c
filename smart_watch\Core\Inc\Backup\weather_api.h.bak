/**
  ******************************************************************************
  * @file    weather_api.h
  * @brief   天气API模块头文件 - OpenWeatherMap API接口
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

#ifndef __WEATHER_API_H
#define __WEATHER_API_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "wifi_manager.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 天气状态枚举
 */
typedef enum {
    WEATHER_STATUS_UNKNOWN = 0,     // 未知
    WEATHER_STATUS_CLEAR,           // 晴天
    WEATHER_STATUS_CLOUDS,          // 多云
    WEATHER_STATUS_RAIN,            // 雨天
    WEATHER_STATUS_SNOW,            // 雪天
    WEATHER_STATUS_MIST,            // 雾天
    WEATHER_STATUS_THUNDERSTORM     // 雷暴
} weather_status_t;

/**
 * @brief 天气数据结构体
 */
typedef struct {
    // 基本天气信息
    float temperature;              // 温度 (°C)
    float humidity;                 // 湿度 (%)
    float pressure;                 // 气压 (hPa)
    weather_status_t status;        // 天气状态
    char description[32];           // 天气描述
    
    // 位置信息
    char city_name[32];             // 城市名称
    char country[8];                // 国家代码
    
    // 时间信息
    uint32_t update_time;           // 更新时间戳
    uint32_t sunrise;               // 日出时间戳
    uint32_t sunset;                // 日落时间戳
    
    // 数据有效性
    uint8_t data_valid;             // 数据是否有效
    uint8_t last_error;             // 最后一次错误代码
} weather_data_t;

/**
 * @brief HTTP响应结构体
 */
typedef struct {
    char* data;                     // 响应数据
    size_t size;                    // 数据大小
    int status_code;                // HTTP状态码
} http_response_t;

/* Exported constants --------------------------------------------------------*/

// OpenWeatherMap API配置
#define WEATHER_API_HOST            "api.openweathermap.org"
#define WEATHER_API_PORT            80
#define WEATHER_API_PATH            "/data/2.5/weather"

// 默认配置
#define WEATHER_DEFAULT_CITY        "Beijing"
#define WEATHER_DEFAULT_UNITS       "metric"      // 摄氏度
#define WEATHER_DEFAULT_LANG        "zh_cn"       // 中文

// 缓冲区大小 - 优化内存使用
#define WEATHER_HTTP_BUFFER_SIZE    512     // HTTP缓冲区 (减少到512)
#define WEATHER_JSON_BUFFER_SIZE    512     // JSON缓冲区 (减少到512)
#define WEATHER_URL_BUFFER_SIZE     256     // URL缓冲区 (减少到256)

// 超时时间
#define WEATHER_HTTP_TIMEOUT        15000         // HTTP请求超时 15秒
#define WEATHER_CONNECT_TIMEOUT     10000         // TCP连接超时 10秒

// 更新间隔
#define WEATHER_UPDATE_INTERVAL     1800000       // 30分钟 (毫秒)
#define WEATHER_MIN_UPDATE_INTERVAL 300000        // 最小5分钟间隔

/* Exported macro ------------------------------------------------------------*/

// 调试输出宏
#ifdef WEATHER_DEBUG
#define WEATHER_LOG(fmt, ...) printf("[WEATHER] " fmt "\r\n", ##__VA_ARGS__)
#else
#define WEATHER_LOG(fmt, ...)
#endif

// 天气状态转换宏
#define WEATHER_IS_SUNNY(status)    ((status) == WEATHER_STATUS_CLEAR)
#define WEATHER_IS_CLOUDY(status)   ((status) == WEATHER_STATUS_CLOUDS)
#define WEATHER_IS_RAINY(status)    ((status) == WEATHER_STATUS_RAIN)

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 天气API模块初始化
 * @retval 0: 成功, -1: 失败
 */
int Weather_Init(void);

/**
 * @brief 设置API密钥
 * @param api_key: OpenWeatherMap API密钥
 * @retval 0: 成功, -1: 失败
 */
int Weather_SetAPIKey(const char* api_key);

/**
 * @brief 设置查询城市
 * @param city: 城市名称
 * @retval 0: 成功, -1: 失败
 */
int Weather_SetCity(const char* city);

/**
 * @brief 获取天气数据
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_GetData(weather_data_t* weather);

/**
 * @brief 获取指定城市的天气数据
 * @param city: 城市名称
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_GetDataByCity(const char* city, weather_data_t* weather);

/**
 * @brief 发送HTTP GET请求
 * @param url: 请求URL
 * @param response: 响应结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_HTTPGet(const char* url, http_response_t* response);

/**
 * @brief 解析天气JSON数据
 * @param json_data: JSON字符串
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 失败
 */
int Weather_ParseJSON(const char* json_data, weather_data_t* weather);

/**
 * @brief 构建天气API请求URL
 * @param city: 城市名称
 * @param api_key: API密钥
 * @param url_buffer: URL缓冲区
 * @param buffer_size: 缓冲区大小
 * @retval 0: 成功, -1: 失败
 */
int Weather_BuildURL(const char* city, const char* api_key, char* url_buffer, size_t buffer_size);

/**
 * @brief 天气状态转换为字符串
 * @param status: 天气状态
 * @retval 天气状态字符串
 */
const char* Weather_StatusToString(weather_status_t status);

/**
 * @brief 天气状态转换为图标字符
 * @param status: 天气状态
 * @retval 天气图标字符
 */
char Weather_StatusToIcon(weather_status_t status);

/**
 * @brief 检查是否需要更新天气数据
 * @param last_update: 上次更新时间戳
 * @retval 1: 需要更新, 0: 不需要更新
 */
int Weather_NeedUpdate(uint32_t last_update);

/**
 * @brief 获取缓存的天气数据
 * @param weather: 天气数据结构体指针
 * @retval 0: 成功, -1: 无缓存数据
 */
int Weather_GetCachedData(weather_data_t* weather);

/**
 * @brief 天气数据更新任务（供FreeRTOS调用）
 * @param argument: 任务参数
 */
void Weather_UpdateTask(void *argument);

#ifdef __cplusplus
}
#endif

#endif /* __WEATHER_API_H */
