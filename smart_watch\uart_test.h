/**
 * @file uart_test.h
 * @brief UART通信测试工具头文件
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#ifndef __UART_TEST_H__
#define __UART_TEST_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 基本连通性测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Basic(void);

/**
 * @brief 波特率测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Baudrate(void);

/**
 * @brief 回环测试
 * @retval 测试结果字符串
 */
const char* UART_Test_Loopback(void);

/**
 * @brief 完整的UART诊断测试
 * @retval 诊断结果字符串
 */
const char* UART_Test_Complete(void);

/**
 * @brief UART接收中断回调（测试用）
 * @param huart: UART句柄
 */
void Test_UART_RxCallback(UART_HandleTypeDef *huart);

#ifdef __cplusplus
}
#endif

#endif /* __UART_TEST_H__ */
