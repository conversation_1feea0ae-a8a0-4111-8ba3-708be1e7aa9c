/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdint.h>
#include "AHT10.h"
#include "SGP30.h"
#include "OLED.h"
#include "DS1302.h"
#include <stdio.h>
#include <string.h>


// 外部变量声明
extern uint8_t aht10_calibrated;
extern uint8_t sgp30_initialized;

// 临时注释WiFi相关配置和变量
/*
// WiFi配置 - 请根据您的实际网络修改
#define WIFI_SSID       "Xiaomi_8D90"      // 您的WiFi名称
#define WIFI_PASSWORD   "abcd8888"         // 您的WiFi密码
#define WEATHER_API_KEY "b2a509e1f015c8e35513d09a136e3cc4" // OpenWeatherMap API密钥

// 全局天气数据
weather_data_t g_weather_data = {0};
uint8_t g_weather_data_valid = 0;
unsigned long g_last_weather_update = 0;
*/

// 全局传感器数据变量
AHT10_Data_t g_aht_data = {0};
SGP30_Data_t g_sgp_data = {0};
uint8_t sgp30_enable = 1; // SGP30使能标志（可用于手动控制）

// 全局时钟数据
DS1302_Time_t g_current_time = {0};

// 显示控制变量
typedef enum {
    DISPLAY_MODE_CLOCK = 0,     // 时钟模式（默认）
    DISPLAY_MODE_SENSOR,        // 传感器模式
    DISPLAY_MODE_ALARM,         // 闹钟模式
    DISPLAY_MODE_DATA_LOG,      // 数据记录模式 (新增)
    DISPLAY_MODE_TIMER,         // 计时器模式
    DISPLAY_MODE_STOPWATCH,     // 秒表模式
    // DISPLAY_MODE_WEATHER,       // 天气模式 (临时注释)
    DISPLAY_MODE_COUNT
} display_mode_t;

volatile display_mode_t g_display_mode = DISPLAY_MODE_CLOCK;
volatile unsigned long g_sensor_display_timeout = 0;  // 传感器显示超时时间
#define SENSOR_DISPLAY_TIMEOUT_MS 10000  // 10秒后自动返回时钟显示

// 多闹钟数据结构
typedef struct {
    uint8_t hour;           // 小时 (0-23)
    uint8_t minute;         // 分钟 (0-59)
    uint8_t enabled;        // 是否启用 (0/1)
    uint8_t triggered;      // 是否已触发 (0/1)
    uint8_t repeat_days;    // 重复日期位掩码 (bit0=周一, bit6=周日)
    char label[8];          // 闹钟标签
} multi_alarm_t;

// 多闹钟管理结构
typedef struct {
    multi_alarm_t alarms[5];    // 5个闹钟
    uint8_t current_alarm;      // 当前选中的闹钟 (0-4)
    uint8_t total_alarms;       // 已设置的闹钟数量
} alarm_manager_t;

// 兼容性：保留原有的alarm_t类型定义
typedef multi_alarm_t alarm_t;

// 数据记录结构（优化为小屏幕）
typedef struct {
    uint8_t hour;           // 小时
    uint8_t minute;         // 分钟
    int8_t temperature;     // 温度 (整数部分，-50到+50°C)
    uint8_t humidity;       // 湿度 (整数部分，0-100%)
    uint16_t co2;           // CO2浓度 (ppm)
} data_point_t;

// 简化的数据记录器（144个点，24小时每10分钟一个）
typedef struct {
    data_point_t points[144];   // 24小时数据点
    uint8_t current_index;      // 当前写入位置
    uint8_t total_points;       // 总数据点数
    unsigned long last_record_time;  // 上次记录时间
} data_logger_t;

// 简化的统计数据
typedef struct {
    int8_t temp_max;         // 最高温度
    int8_t temp_min;         // 最低温度
    uint8_t humi_max;        // 最高湿度
    uint8_t humi_min;        // 最低湿度
    uint8_t temp_max_hour;   // 最高温度时间（小时）
    uint8_t temp_max_minute; // 最高温度时间（分钟）
    uint8_t temp_min_hour;   // 最低温度时间（小时）
    uint8_t temp_min_minute; // 最低温度时间（分钟）
} daily_stats_t;

// 计时器数据结构（优化溢出风险）
typedef struct {
    unsigned long total_seconds;     // 总秒数（最大约136年）
    unsigned long remaining_seconds; // 剩余秒数（最大约136年）
    uint8_t running;           // 是否运行中
    uint8_t finished;          // 是否已完成
} timer_t;

// 秒表数据结构
typedef struct {
    unsigned long elapsed_ms;       // 已过时间（毫秒）
    unsigned long start_time;       // 开始时间
    uint8_t running;           // 是否运行中
} stopwatch_t;

// 重复模式定义
#define REPEAT_MONDAY    (1 << 0)  // 0x01
#define REPEAT_TUESDAY   (1 << 1)  // 0x02
#define REPEAT_WEDNESDAY (1 << 2)  // 0x04
#define REPEAT_THURSDAY  (1 << 3)  // 0x08
#define REPEAT_FRIDAY    (1 << 4)  // 0x10
#define REPEAT_SATURDAY  (1 << 5)  // 0x20
#define REPEAT_SUNDAY    (1 << 6)  // 0x40

#define REPEAT_WEEKDAYS  (REPEAT_MONDAY | REPEAT_TUESDAY | REPEAT_WEDNESDAY | REPEAT_THURSDAY | REPEAT_FRIDAY)
#define REPEAT_WEEKEND   (REPEAT_SATURDAY | REPEAT_SUNDAY)
#define REPEAT_EVERYDAY  (0x7F)
#define REPEAT_ONCE      (0x00)

// 全局变量
alarm_manager_t g_alarm_manager = {
    .alarms = {
        {6, 30, 0, 0, REPEAT_ONCE, "Alarm1"},  // 默认闹钟1
        {0, 0, 0, 0, REPEAT_ONCE, "Alarm2"},   // 空闹钟2
        {0, 0, 0, 0, REPEAT_ONCE, "Alarm3"},   // 空闹钟3
        {0, 0, 0, 0, REPEAT_ONCE, "Alarm4"},   // 空闹钟4
        {0, 0, 0, 0, REPEAT_ONCE, "Alarm5"}    // 空闹钟5
    },
    .current_alarm = 0,
    .total_alarms = 1
};

// 兼容性：保留原有的g_alarm变量，指向第一个闹钟
#define g_alarm (g_alarm_manager.alarms[0])

timer_t g_timer = {0, 0, 0, 0};   // 计时器
stopwatch_t g_stopwatch = {0, 0, 0}; // 秒表

// 数据记录全局变量
data_logger_t g_data_logger = {0};
daily_stats_t g_daily_stats = {0};
uint8_t g_data_view_mode = 0;  // 数据查看模式 (0=概览, 1=趋势, 2=统计)

// 当前传感器数据（用于数据记录）
float g_current_temperature = 0.0f;
float g_current_humidity = 0.0f;
uint16_t g_current_co2 = 0;
volatile uint8_t g_display_updating = 0;  // 显示更新标志
volatile uint32_t g_system_watchdog = 0;   // 系统看门狗计数器
volatile uint8_t g_force_display_update = 0; // 强制显示更新标志

// 设置功能相关变量
typedef enum {
    SETTING_MODE_NONE = 0,      // 正常模式
    SETTING_MODE_TIME,          // 时间设置
    SETTING_MODE_ALARM,         // 闹钟设置
    SETTING_MODE_MULTI_ALARM,   // 多闹钟编辑模式
    SETTING_MODE_THEME,         // 主题设置模式 (新增)
    SETTING_MODE_TIMER          // 计时器设置
} setting_mode_t;

typedef enum {
    TIME_ITEM_YEAR = 0,         // 年
    TIME_ITEM_MONTH,            // 月
    TIME_ITEM_DATE,             // 日
    TIME_ITEM_HOUR,             // 时
    TIME_ITEM_MINUTE,           // 分
    TIME_ITEM_COUNT
} time_setting_item_t;

typedef enum {
    ALARM_ITEM_HOUR = 0,        // 闹钟小时
    ALARM_ITEM_MINUTE,          // 闹钟分钟
    ALARM_ITEM_ENABLE,          // 闹钟开关
    ALARM_ITEM_COUNT
} alarm_setting_item_t;

typedef enum {
    TIMER_ITEM_HOUR = 0,        // 计时器小时
    TIMER_ITEM_MINUTE,          // 计时器分钟
    TIMER_ITEM_SECOND,          // 计时器秒钟
    TIMER_ITEM_COUNT
} timer_setting_item_t;

// 多闹钟编辑项目
typedef enum {
    MULTI_ALARM_ITEM_HOUR = 0,  // 小时
    MULTI_ALARM_ITEM_MINUTE,    // 分钟
    MULTI_ALARM_ITEM_ENABLED,   // 开关
    MULTI_ALARM_ITEM_REPEAT,    // 重复模式
    MULTI_ALARM_ITEM_COUNT
} multi_alarm_item_t;

volatile setting_mode_t g_setting_mode = SETTING_MODE_NONE;
volatile uint8_t g_setting_item = 0;        // 当前设置项
volatile uint32_t g_setting_timeout = 0;    // 设置超时时间
DS1302_Time_t g_temp_time;                  // 临时时间变量
alarm_t g_temp_alarm;                       // 临时闹钟变量
multi_alarm_t g_temp_multi_alarm;           // 临时多闹钟变量
uint8_t g_editing_alarm_id;                 // 正在编辑的闹钟ID
timer_t g_temp_timer;                       // 临时计时器变量

#define SETTING_TIMEOUT_MS 30000  // 30秒设置超时

// 蜂鸣器相关定义
typedef enum {
    BUZZER_OFF = 0,           // 关闭
    BUZZER_ALARM,             // 闹钟响铃
    BUZZER_TIMER,             // 计时器响铃
    BUZZER_KEY_BEEP           // 按键音效
} buzzer_mode_t;

typedef struct {
    buzzer_mode_t mode;       // 当前模式
    uint8_t active;           // 是否激活
    uint32_t start_time;      // 开始时间
    uint32_t duration;        // 持续时间(ms)
    uint32_t pattern_time;    // 模式计时
    uint8_t pattern_state;    // 模式状态(0=关闭, 1=响铃)
} buzzer_t;

volatile buzzer_t g_buzzer = {BUZZER_OFF, 0, 0, 0, 0, 0};

// 调试变量
volatile uint32_t g_key_press_time = 0;
volatile uint32_t g_key_response_time = 0;
volatile uint32_t g_debug_counter = 0;  // 调试计数器

// 中断标志
volatile uint8_t g_key_interrupt_flag = 0;

// 按键中断标志 - 每个按键独立标志
volatile uint8_t g_key_up_interrupt = 0;
volatile uint8_t g_key_down_interrupt = 0;
volatile uint8_t g_key_menu_interrupt = 0;
volatile uint8_t g_key_set_interrupt = 0;

// 按键中断时间戳
volatile uint32_t g_key_up_interrupt_time = 0;
volatile uint32_t g_key_down_interrupt_time = 0;
volatile uint32_t g_key_menu_interrupt_time = 0;
volatile uint32_t g_key_set_interrupt_time = 0;

// 快速响应标志
volatile uint8_t g_fast_response_mode = 1;  // 启用快速响应模式

// 蜂鸣器控制宏(低电平响应)
#define BUZZER_ON()  HAL_GPIO_WritePin(BUZZER_PIN_GPIO_Port, BUZZER_PIN_Pin, GPIO_PIN_RESET)
#define BUZZER_OFF() HAL_GPIO_WritePin(BUZZER_PIN_GPIO_Port, BUZZER_PIN_Pin, GPIO_PIN_SET)
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};
/* Definitions for KeyTask */
osThreadId_t KeyTaskHandle;
const osThreadAttr_t KeyTask_attributes = {
  .name = "KeyTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityHigh,
};
/* Definitions for SensorTask */
osThreadId_t SensorTaskHandle;
const osThreadAttr_t SensorTask_attributes = {
  .name = "SensorTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityBelowNormal,
};
/* Definitions for DisplayTask */
osThreadId_t DisplayTaskHandle;
const osThreadAttr_t DisplayTask_attributes = {
  .name = "DisplayTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityAboveNormal,
};
/* Definitions for WiFiTask */
osThreadId_t WiFiTaskHandle;
const osThreadAttr_t WiFiTask_attributes = {
  .name = "WiFiTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityLow,
};
/* Definitions for KeyEventQueue */
osMessageQueueId_t KeyEventQueueHandle;
const osMessageQueueAttr_t KeyEventQueue_attributes = {
  .name = "KeyEventQueue"
};
/* Definitions for SensorDataQueue */
osMessageQueueId_t SensorDataQueueHandle;
const osMessageQueueAttr_t SensorDataQueue_attributes = {
  .name = "SensorDataQueue"
};
/* Definitions for DisplayMutex */
osMutexId_t DisplayMutexHandle;
const osMutexAttr_t DisplayMutex_attributes = {
  .name = "DisplayMutex"
};
/* Definitions for TimeUpdateSem */
osSemaphoreId_t TimeUpdateSemHandle;
const osSemaphoreAttr_t TimeUpdateSem_attributes = {
  .name = "TimeUpdateSem"
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void Display_Clock_Interface(void);
void Display_Sensor_Interface(float temperature, float humidity, uint16_t tvoc, uint16_t eco2);
void Display_Alarm_Interface(void);
void Display_Timer_Interface(void);
void Display_Stopwatch_Interface(void);
void Update_Timer(void);
void Update_Stopwatch(void);
void Check_Alarm(void);

// 设置功能函数声明
void Enter_Setting_Mode(void);
void Next_Setting_Item(void);
void Increase_Setting_Value(void);
void Decrease_Setting_Value(void);
void Save_And_Exit_Setting(void);
void Display_Time_Setting(void);
void Display_Alarm_Setting(void);
void Display_Multi_Alarm_Setting(void);
void Display_Timer_Setting(void);

// 蜂鸣器功能函数声明
void Buzzer_Start_Alarm(void);
void Buzzer_Start_Timer(void);
void Buzzer_Key_Beep(void);
void Buzzer_Stop(void);
void Buzzer_Update(void);
void Handle_Key_Beep(void);  // 统一的按键音效处理

// 多闹钟管理函数声明
uint8_t Add_New_Alarm(uint8_t hour, uint8_t minute, const char* label);
void Delete_Alarm(uint8_t alarm_id);
uint8_t Get_Next_Alarm(void);
uint8_t Check_Alarm_Repeat(multi_alarm_t *alarm, uint8_t current_day);
void Check_Multi_Alarms(void);
void Display_Repeat_Pattern(uint8_t repeat_days, char* buffer);
void Display_Alarm_Simple(void);

// 数据记录功能函数声明
void Record_Sensor_Data(void);
void Update_Daily_Stats(data_point_t* point);
void Display_Data_Log(void);
void Reset_Daily_Stats(void);

// GPIO中断回调函数
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin);
/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void *argument);
void StartKeyTask(void *argument);
void StartSensorTask(void *argument);
void StartDisplayTask(void *argument);
void StartWiFiTask(void *argument);

// 函数声明
// void Display_Weather_Interface(void);  // 临时注释

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */
  /* Create the mutex(es) */
  /* creation of DisplayMutex */
  DisplayMutexHandle = osMutexNew(&DisplayMutex_attributes);

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* Create the semaphores(s) */
  /* creation of TimeUpdateSem */
  TimeUpdateSemHandle = osSemaphoreNew(1, 1, &TimeUpdateSem_attributes);

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* Create the queue(s) */
  /* creation of KeyEventQueue */
  KeyEventQueueHandle = osMessageQueueNew (5, sizeof(uint8_t), &KeyEventQueue_attributes);

  /* creation of SensorDataQueue */
  SensorDataQueueHandle = osMessageQueueNew (2, sizeof(uint32_t), &SensorDataQueue_attributes);

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of defaultTask */
  defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);

  /* creation of KeyTask */
  KeyTaskHandle = osThreadNew(StartKeyTask, NULL, &KeyTask_attributes);

  /* creation of SensorTask */
  SensorTaskHandle = osThreadNew(StartSensorTask, NULL, &SensorTask_attributes);

  /* creation of DisplayTask */
  DisplayTaskHandle = osThreadNew(StartDisplayTask, NULL, &DisplayTask_attributes);

  /* creation of WiFiTask - 临时禁用调试 */
  // WiFiTaskHandle = osThreadNew(StartWiFiTask, NULL, &WiFiTask_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void *argument)
{
  /* USER CODE BEGIN StartDefaultTask */
  /* Infinite loop */
  for(;;)
  {
    // 系统心跳，每10秒执行一次
    // 可以在这里添加系统监控代码
    osDelay(10000);
  }
  /* USER CODE END StartDefaultTask */
}

/* USER CODE BEGIN Header_StartKeyTask */
/**
* @brief Function implementing the KeyTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartKeyTask */
void StartKeyTask(void *argument)
{
  /* USER CODE BEGIN StartKeyTask */

  // 按键状态变量
  static uint8_t key_up_last = 1, key_down_last = 1, key_menu_last = 1, key_set_last = 1;
  static unsigned long key_up_press_time = 0, key_down_press_time = 0, key_menu_press_time = 0, key_set_press_time = 0;
  uint8_t key_up_current, key_down_current, key_menu_current, key_set_current;

  #define KEY_DEBOUNCE_TIME 5   // 5ms防抖时间 - 极速响应

  /* Infinite loop */
  for(;;)
  {
    // 读取当前按键状态（按下为低电平）
    key_up_current = HAL_GPIO_ReadPin(KEY_UP_GPIO_Port, KEY_UP_Pin);
    key_down_current = HAL_GPIO_ReadPin(KEY_DOWN_GPIO_Port, KEY_DOWN_Pin);
    key_menu_current = HAL_GPIO_ReadPin(KEY_MENU_GPIO_Port, KEY_MENU_Pin);
    key_set_current = HAL_GPIO_ReadPin(KEY_SET_GPIO_Port, KEY_SET_Pin);

    unsigned long current_time = HAL_GetTick();

    // KEY_UP按键处理（向上切换模式或功能操作）- 立即响应版本
    static unsigned long last_up_action_time = 0;

    if (key_up_last == 1 && key_up_current == 0) {
      // 按键按下 - 立即执行功能（防抖）
      if (current_time - last_up_action_time >= KEY_DEBOUNCE_TIME) {
        last_up_action_time = current_time;
        g_key_press_time = current_time;  // 记录按键时间
        g_debug_counter++;  // 增加调试计数器

        // 音效已禁用

        // 立即执行功能 - 不等待释放
        if (g_setting_mode != SETTING_MODE_NONE) {
          // 设置模式：增加当前设置项的值
          Increase_Setting_Value();
          g_force_display_update = 1;
        } else {
          // 正常模式：根据当前模式执行不同操作
          switch(g_display_mode) {
          case DISPLAY_MODE_CLOCK:
            // 时钟模式：切换到传感器模式（重新启用）
            g_display_mode = DISPLAY_MODE_SENSOR;
            g_sensor_display_timeout = current_time + SENSOR_DISPLAY_TIMEOUT_MS;
            g_force_display_update = 1;  // 强制立即更新显示
            osThreadYield();  // 快速响应：让出CPU给DisplayTask
            break;
          case DISPLAY_MODE_SENSOR:
            // 传感器模式：切换显示状态
            // 这里可以用来切换显示详细数据
            break;
          case DISPLAY_MODE_ALARM:
            // 闹钟模式：切换开关
            g_alarm.enabled = !g_alarm.enabled;
            g_alarm.triggered = 0;  // 重置触发状态
            g_force_display_update = 1;
            break;
          case DISPLAY_MODE_TIMER:
            // 计时器模式：启动/暂停
            if (g_timer.running) {
              g_timer.running = 0;  // 暂停
            } else if (g_timer.remaining_seconds > 0) {
              g_timer.running = 1;  // 启动
            }
            g_force_display_update = 1;
            break;
          case DISPLAY_MODE_STOPWATCH:
            // 秒表模式：启动/暂停
            if (g_stopwatch.running) {
              g_stopwatch.running = 0;  // 暂停
              g_stopwatch.elapsed_ms += HAL_GetTick() - g_stopwatch.start_time;
            } else {
              g_stopwatch.running = 1;  // 启动
              g_stopwatch.start_time = HAL_GetTick();
            }
            g_force_display_update = 1;
            break;
          default:
            break;
          }
        }
      }
    }

    // KEY_DOWN按键处理（返回时钟或重置功能）
    if (key_down_last == 1 && key_down_current == 0) {
      // 按键按下 - 立即触发音效
      key_down_press_time = current_time;
      // 音效已禁用
    } else if (key_down_last == 0 && key_down_current == 1) {
      if (current_time - key_down_press_time >= KEY_DEBOUNCE_TIME) {

        // 检查是否在设置模式
        if (g_setting_mode != SETTING_MODE_NONE) {
          // 设置模式：减少当前设置项的值
          Decrease_Setting_Value();
          g_force_display_update = 1;
        } else {
          // 正常模式：根据当前模式执行不同操作
          switch(g_display_mode) {
          case DISPLAY_MODE_TIMER:
            // 计时器模式：重置
            g_timer.running = 0;
            g_timer.remaining_seconds = g_timer.total_seconds;
            g_timer.finished = 0;
            break;
          case DISPLAY_MODE_STOPWATCH:
            // 秒表模式：重置
            g_stopwatch.running = 0;
            g_stopwatch.elapsed_ms = 0;
            g_stopwatch.start_time = 0;
            break;
          default:
            // 其他模式：返回时钟显示
            g_display_mode = DISPLAY_MODE_CLOCK;
            break;
          }
        }
      }
    }

    // KEY_MENU按键处理（循环切换显示模式）- 立即响应版本
    static unsigned long last_menu_action_time = 0;

    if (key_menu_last == 1 && key_menu_current == 0) {
      // 按键按下 - 立即执行功能（防抖）
      if (current_time - last_menu_action_time >= KEY_DEBOUNCE_TIME) {
        last_menu_action_time = current_time;

        // 音效已禁用

        // 立即执行功能
        if (g_setting_mode != SETTING_MODE_NONE) {
          // 设置模式：保存并退出设置
          Save_And_Exit_Setting();
          g_force_display_update = 1;
        } else {
          // 正常模式：循环切换显示模式
          uint8_t next_mode = (g_display_mode + 1) % DISPLAY_MODE_COUNT;

          // 安全检查：确保模式值有效
          if (next_mode < DISPLAY_MODE_COUNT) {
            g_display_mode = (display_mode_t)next_mode;
            g_force_display_update = 1;  // 强制立即更新显示

            // 快速响应：立即触发DisplayTask
            osThreadYield();  // 让出CPU给DisplayTask

            // 如果切换到传感器模式，设置超时
            if (g_display_mode == DISPLAY_MODE_SENSOR) {
              g_sensor_display_timeout = current_time + SENSOR_DISPLAY_TIMEOUT_MS;
            }

            // 如果切换到计时器模式，设置默认时间（如果未设置）
            if (g_display_mode == DISPLAY_MODE_TIMER && g_timer.total_seconds == 0) {
              g_timer.total_seconds = 300;  // 默认5分钟
              g_timer.remaining_seconds = 300;
            }
          }
        }
      }
    }

    // 检查传感器显示超时
    if (g_display_mode == DISPLAY_MODE_SENSOR && current_time >= g_sensor_display_timeout) {
      g_display_mode = DISPLAY_MODE_CLOCK;  // 自动返回时钟显示
    }

    // 紧急恢复检测：如果所有按键同时按下超过3秒，重置系统状态
    static unsigned long emergency_start = 0;
    if (key_up_current == 0 && key_down_current == 0 && key_menu_current == 0) {
      if (emergency_start == 0) {
        emergency_start = current_time;
      } else if (current_time - emergency_start > 3000) {
        // 紧急恢复：重置所有状态
        g_display_mode = DISPLAY_MODE_CLOCK;
        g_display_updating = 0;
        g_timer.running = 0;
        g_stopwatch.running = 0;
        emergency_start = 0;

        // 清空传感器队列
        unsigned long dummy;
        while (osMessageQueueGet(SensorDataQueueHandle, &dummy, NULL, 0) == osOK);
      }
    } else {
      emergency_start = 0;  // 重置紧急恢复计时
    }

    // KEY_SET按键处理（设置功能）
    static uint8_t long_press_triggered = 0;  // 长按触发标志

    if (key_set_last == 1 && key_set_current == 0) {
      // 按键按下 - 立即触发音效
      key_set_press_time = current_time;
      long_press_triggered = 0;  // 重置长按标志
      // 音效已禁用
    } else if (key_set_current == 0) {
      // 按键持续按下，检查是否达到长按时间
      unsigned long press_duration = current_time - key_set_press_time;
      if (press_duration >= 2000 && !long_press_triggered) {
        // 停止蜂鸣器（如果正在响铃）
        if (g_buzzer.active) {
          Buzzer_Stop();
        }
        // 音效已禁用
        // 长按2秒：立即进入设置模式
        Enter_Setting_Mode();
        g_force_display_update = 1;
        long_press_triggered = 1;  // 标记已触发，避免重复触发
      }
    } else if (key_set_last == 0 && key_set_current == 1) {
      // 按键释放
      unsigned long press_duration = current_time - key_set_press_time;
      if (press_duration >= KEY_DEBOUNCE_TIME && press_duration < 2000) {
        // 短按：在设置模式中切换设置项（音效已在按下时触发）
        if (g_setting_mode != SETTING_MODE_NONE) {
          Next_Setting_Item();
          g_force_display_update = 1;
        }
      }
      long_press_triggered = 0;  // 重置长按标志
    }

    // 更新系统看门狗
    g_system_watchdog = current_time;

    // 更新按键状态
    key_up_last = key_up_current;
    key_down_last = key_down_current;
    key_menu_last = key_menu_current;
    key_set_last = key_set_current;

    osDelay(1);  // 1ms扫描间隔 - 极速响应
  }
  /* USER CODE END StartKeyTask */
}

/* USER CODE BEGIN Header_StartSensorTask */
/**
* @brief Function implementing the SensorTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartSensorTask */
void StartSensorTask(void *argument)
{
  /* USER CODE BEGIN StartSensorTask */
  AHT10_Status_t aht_status;
  SGP30_Status_t sgp_status;
  uint32_t sensor_data_packed;
  uint32_t error_code = 0;

  // 等待系统稳定（减少延时）
  osDelay(200);

  // 初始化AHT10传感器
  uint8_t aht_test_result = AHT10_SimpleTest();
  if (aht_test_result == 0) {
    AHT10_Init();
    aht10_calibrated = 1;
  }

  // 初始化SGP30传感器
  uint8_t sgp_present = SGP30_IsPresent();
  if (sgp_present) {
    sgp_status = SGP30_Init();
    if (sgp_status != SGP30_OK) {
      // SGP30初始化失败
      error_code = 0x99990000 | sgp_status;
      // 非阻塞发送
      osMessageQueuePut(SensorDataQueueHandle, &error_code, 0, 0);
    }
  } else {
    // SGP30不存在
    error_code = 0x88880000 | 0xFF;
    // 非阻塞发送
    osMessageQueuePut(SensorDataQueueHandle, &error_code, 0, 0);
  }

  /* Infinite loop */
  for(;;)
  {
    // 读取AHT10温湿度数据（添加看门狗保护）
    if (aht10_calibrated) {
      // 设置传感器读取标志
      g_display_updating = 1;  // 借用这个标志防止按键干扰
      aht_status = AHT10_GetTemperatureHumidity(&g_aht_data);
      g_display_updating = 0;  // 清除标志
    } else {
      aht_status = AHT10_ERROR;
    }

    // 读取SGP30空气质量数据（降低频率以减少发热）
    static uint8_t sgp_counter = 0;
    if (sgp30_initialized && sgp30_enable && (sgp_counter % 4 == 0)) {
      // 每20秒读取一次SGP30（进一步降低频率）
      g_display_updating = 1;  // 设置保护标志
      sgp_status = SGP30_GetAirQuality(&g_sgp_data);
      g_display_updating = 0;  // 清除标志

      // 如果AHT10数据有效，设置湿度补偿
      if (aht_status == AHT10_OK) {
        SGP30_SetHumidity(g_aht_data.humidity);
      }
    } else {
      sgp_status = SGP30_OK; // 保持上次的状态
    }
    sgp_counter++;

    // 发送传感器数据到队列（简化版本，避免队列阻塞）
    static uint8_t send_counter = 0;
    send_counter++;

    // 只在特定周期发送数据，避免队列满
    if (send_counter >= 2) {  // 每10秒发送一次（5秒*2）
      send_counter = 0;

      if (aht_status == AHT10_OK && sgp_status == SGP30_OK) {
        // 所有传感器数据都有效，直接打包SGP30数据
        // 格式：高16位=TVOC，低16位=CO2
        uint16_t tvoc_val = (g_sgp_data.tvoc_ppb > 65535) ? 65535 : g_sgp_data.tvoc_ppb;
        uint16_t eco2_val = (g_sgp_data.eco2_ppm > 65535) ? 65535 : g_sgp_data.eco2_ppm;
        sensor_data_packed = ((uint32_t)tvoc_val << 16) | eco2_val;
        osMessageQueuePut(SensorDataQueueHandle, &sensor_data_packed, 0, 0);

        // 发送AHT10数据（温湿度）
        uint16_t temp_int = (uint16_t)(g_aht_data.temperature * 100);
        uint16_t humi_int = (uint16_t)(g_aht_data.humidity * 100);
        uint32_t aht_data_packed = ((uint32_t)temp_int << 16) | humi_int | 0x80000000; // 最高位标识AHT数据
        osMessageQueuePut(SensorDataQueueHandle, &aht_data_packed, 0, 0);

      } else if (aht_status == AHT10_OK) {
        // 只有AHT10数据有效
        uint16_t temp_int = (uint16_t)(g_aht_data.temperature * 100);
        uint16_t humi_int = (uint16_t)(g_aht_data.humidity * 100);
        sensor_data_packed = ((uint32_t)temp_int << 16) | humi_int | 0x80000000; // 最高位标识AHT数据
        osMessageQueuePut(SensorDataQueueHandle, &sensor_data_packed, 0, 0);
      } else {
        // 发送错误代码
        error_code = 0xEEEE0000 | aht_status;
        osMessageQueuePut(SensorDataQueueHandle, &error_code, 0, 0);
      }
    }

    // 每5秒读取一次传感器数据（降低SGP30工作频率）
    osDelay(5000);
  }
  /* USER CODE END StartSensorTask */
}

/* USER CODE BEGIN Header_StartDisplayTask */
/**
* @brief Function implementing the DisplayTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDisplayTask */
void StartDisplayTask(void *argument)
{
  /* USER CODE BEGIN StartDisplayTask */
  uint32_t sensor_data_packed;
  static float temperature = 0.0f, humidity = 0.0f;  // 静态变量保存数据
  static uint16_t tvoc = 0, eco2 = 0;                 // 静态变量保存数据
  char line1[20];  // 用于错误信息显示
  osStatus_t queue_status;
  uint8_t data_received = 0; // 标记是否已经接收到有效数据

  // 等待其他任务启动（特别是DS1302初始化完成）
  osDelay(100);

  // 初始化OLED显示
  OLED_PowerOnInit();

  // 立即显示时钟界面，不等待传感器
  DS1302_GetTime(&g_current_time);
  OLED_Clear();
  Display_Clock_Interface();
  OLED_Update();

  /* Infinite loop */
  for(;;)
  {
    // 每秒读取当前时间（确保时钟实时更新）
    DS1302_GetTime(&g_current_time);

    // 后台更新计时器和秒表（无论当前显示什么界面）
    Update_Timer();
    Update_Stopwatch();

    // 更新蜂鸣器状态
    Buzzer_Update();

    // 检查设置超时
    if (g_setting_mode != SETTING_MODE_NONE && HAL_GetTick() > g_setting_timeout) {
      // 设置超时，自动退出设置模式
      g_setting_mode = SETTING_MODE_NONE;
      g_setting_item = 0;
      g_force_display_update = 1;
    }

    // 获取互斥锁
    if (osMutexAcquire(DisplayMutexHandle, 5) == osOK) {   // 极速等待时间

      // 设置显示更新标志
      g_display_updating = 1;

      // 尝试从队列获取传感器数据
      queue_status = osMessageQueueGet(SensorDataQueueHandle, &sensor_data_packed, NULL, 0);

      if (queue_status == osOK) {
        // 检查是否是错误代码
        if ((sensor_data_packed & 0xFFFF0000) == 0x88880000) {
          // SGP30不存在
          OLED_Clear();
          OLED_ShowString(0, 0, "Smart Watch", OLED_8X16);
          OLED_ShowString(0, 20, "SGP30 Not", OLED_8X16);
          OLED_ShowString(0, 40, "Found!", OLED_8X16);
          OLED_Update();
          data_received = 0;

        } else if ((sensor_data_packed & 0xFFFF0000) == 0x99990000) {
          // SGP30初始化失败
          OLED_Clear();
          OLED_ShowString(0, 0, "Smart Watch", OLED_8X16);
          OLED_ShowString(0, 20, "SGP30 Init", OLED_8X16);
          OLED_ShowString(0, 40, "Failed!", OLED_8X16);
          OLED_Update();
          data_received = 0;

        } else if ((sensor_data_packed & 0xFFFF0000) == 0xEEEE0000) {
          // AHT10读取错误
          uint8_t error_code = sensor_data_packed & 0xFF;
          snprintf(line1, sizeof(line1), "AHT Error: %d", error_code);
          OLED_Clear();
          OLED_ShowString(0, 0, "Smart Watch", OLED_8X16);
          OLED_ShowString(0, 20, line1, OLED_8X16);
          OLED_ShowString(0, 40, "Check I2C", OLED_8X16);
          OLED_Update();
          data_received = 0;

        } else if (sensor_data_packed & 0x80000000) {
          // AHT10数据（最高位为1）
          uint16_t temp_int = (uint16_t)((sensor_data_packed >> 16) & 0x7FFF); // 清除标识位
          uint16_t humi_int = (uint16_t)(sensor_data_packed & 0xFFFF);
          temperature = (float)temp_int / 100.0f;
          humidity = (float)humi_int / 100.0f;

          // 保存到全局变量用于数据记录
          g_current_temperature = temperature;
          g_current_humidity = humidity;

          data_received = 1;

        } else if ((sensor_data_packed & 0xFFFF0000) != 0xEEEE0000) {
          // SGP30数据（最高位为0，且不是错误代码）
          tvoc = (uint16_t)(sensor_data_packed >> 16);
          eco2 = (uint16_t)(sensor_data_packed & 0xFFFF);

          // 保存CO2数据用于数据记录
          g_current_co2 = eco2;

          // 保持之前的温湿度数据
          if (temperature > 0 || humidity > 0) {
            data_received = 1;  // 只有在有温湿度数据时才标记为已接收
          }
        }
      }

      // 显示信息
      OLED_Clear();

      // 检查是否在设置模式
      if (g_setting_mode != SETTING_MODE_NONE) {
        // 显示设置界面
        switch(g_setting_mode) {
          case SETTING_MODE_TIME:
            Display_Time_Setting();
            break;
          case SETTING_MODE_ALARM:
            Display_Alarm_Setting();
            break;
          case SETTING_MODE_MULTI_ALARM:
            Display_Multi_Alarm_Setting();
            break;
          case SETTING_MODE_TIMER:
            Display_Timer_Setting();
            break;
          default:
            break;
        }
      } else {
        // 在所有模式下都检查闹钟（确保及时响铃）
        Check_Alarm();

        // 记录传感器数据（每10分钟一次）
        Record_Sensor_Data();

        // 根据按键控制的显示模式显示内容
        if (g_display_mode == DISPLAY_MODE_CLOCK) {
        // 美化的时钟显示界面
        Display_Clock_Interface();

      } else if (g_display_mode == DISPLAY_MODE_SENSOR) {
        // 传感器数据显示（完整版本）
        // 移除重复的OLED_Clear()调用 - 已在第809行调用

        if (data_received) {
          // 直接显示传感器数据，包括空气质量
          Display_Sensor_Interface(temperature, humidity, tvoc, eco2);
        } else {
          // 无数据时显示等待信息
          OLED_ShowString(25, 0, "SENSORS", OLED_8X16);
          OLED_ShowString(10, 20, "Initializing", OLED_8X16);
          OLED_ShowString(10, 40, "Please Wait", OLED_8X16);
        }

      } else if (g_display_mode == DISPLAY_MODE_ALARM) {
        // 闹钟界面 - 显示当前选中的闹钟
        Display_Alarm_Simple();

      } else if (g_display_mode == DISPLAY_MODE_DATA_LOG) {
        // 数据记录界面
        Display_Data_Log();

      } else if (g_display_mode == DISPLAY_MODE_TIMER) {
        // 计时器界面（Update_Timer已在后台运行）
        Display_Timer_Interface();

      } else if (g_display_mode == DISPLAY_MODE_STOPWATCH) {
        // 秒表界面（Update_Stopwatch已在后台运行）
        Display_Stopwatch_Interface();

      // } else if (g_display_mode == DISPLAY_MODE_WEATHER) {
        // 天气界面
        // Display_Weather_Interface();

      } else {
        // 默认显示时钟
        Display_Clock_Interface();
      }
      }  // 结束设置模式检查

      OLED_Update();

      // 清除显示更新标志
      g_display_updating = 0;

      // 释放互斥锁
      osMutexRelease(DisplayMutexHandle);
    }

    // 根据模式调整更新频率，支持强制更新和快速响应
    if (g_force_display_update) {
      g_force_display_update = 0;  // 清除强制更新标志
      if (g_fast_response_mode) {
        // 快速响应模式：无延迟
        continue;  // 立即进入下一次循环
      } else {
        osDelay(1);    // 普通模式：最小延迟
      }
    } else if (g_display_mode == DISPLAY_MODE_STOPWATCH && g_stopwatch.running) {
      osDelay(100);  // 秒表运行时100ms更新
    } else if (g_display_mode == DISPLAY_MODE_SENSOR) {
      osDelay(100);  // 传感器模式100ms更新 - 快速响应
    } else {
      osDelay(1000); // 其他模式1秒更新
    }
  }
  /* USER CODE END StartDisplayTask */
}

/* USER CODE BEGIN Header_StartWiFiTask */
/**
* @brief Function implementing the WiFiTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartWiFiTask */
void StartWiFiTask(void *argument)
{
  /* USER CODE BEGIN StartWiFiTask */

  // 临时禁用WiFi功能进行调试
  /* Infinite loop */
  for(;;)
  {
    // 简单的延时，不执行WiFi功能
    osDelay(5000);
  }

  // 以下是原WiFi代码，暂时注释
  /*
  // WiFi任务状态
  typedef enum {
    WIFI_TASK_INIT,
    WIFI_TASK_CONNECTING,
    WIFI_TASK_CONNECTED,
    WIFI_TASK_UPDATING_WEATHER,
    WIFI_TASK_IDLE,
    WIFI_TASK_ERROR
  } wifi_task_state_t;

  wifi_task_state_t wifi_state = WIFI_TASK_INIT;
  uint32_t last_weather_request = 0;
  uint32_t wifi_retry_count = 0;
  const uint32_t WEATHER_UPDATE_INTERVAL_MS = 30 * 60 * 1000; // 30分钟
  const uint32_t WIFI_RETRY_INTERVAL_MS = 60 * 1000; // 1分钟重试间隔

  // 原WiFi代码已注释，等调试完成后恢复
  */
  /* USER CODE END StartWiFiTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/**
 * @brief 显示美化的时钟界面
 */
void Display_Clock_Interface(void)
{
  char time_str[20], date_str[20], day_str[10];
  const char* day_names[] = {"", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};
  uint8_t time_len, date_len;

  // 优化的字符串构建 - 避免多次snprintf调用
  // 手动构建时间字符串（更快）
  time_str[0] = '0' + (g_current_time.hour / 10);
  time_str[1] = '0' + (g_current_time.hour % 10);
  time_str[2] = ':';
  time_str[3] = '0' + (g_current_time.minute / 10);
  time_str[4] = '0' + (g_current_time.minute % 10);
  time_str[5] = ':';
  time_str[6] = '0' + (g_current_time.second / 10);
  time_str[7] = '0' + (g_current_time.second % 10);
  time_str[8] = '\0';

  // 手动构建日期字符串（更快）
  date_str[0] = '0' + (g_current_time.year / 1000);
  date_str[1] = '0' + ((g_current_time.year / 100) % 10);
  date_str[2] = '0' + ((g_current_time.year / 10) % 10);
  date_str[3] = '0' + (g_current_time.year % 10);
  date_str[4] = '-';
  date_str[5] = '0' + (g_current_time.month / 10);
  date_str[6] = '0' + (g_current_time.month % 10);
  date_str[7] = '-';
  date_str[8] = '0' + (g_current_time.date / 10);
  date_str[9] = '0' + (g_current_time.date % 10);
  date_str[10] = '\0';

  // 星期字符串（简化）
  if (g_current_time.day >= 1 && g_current_time.day <= 7) {
    day_str[0] = day_names[g_current_time.day][0];
    day_str[1] = day_names[g_current_time.day][1];
    day_str[2] = day_names[g_current_time.day][2];
    day_str[3] = '\0';
  } else {
    day_str[0] = '-'; day_str[1] = '-'; day_str[2] = '-'; day_str[3] = '\0';
  }

  // 计算居中位置（假设屏幕宽度128像素，8x16字体宽度8像素）
  time_len = strlen(time_str);
  date_len = strlen(date_str);

  // 居中显示时间
  OLED_ShowString((128 - time_len * 8) / 2, 0, time_str, OLED_8X16);

  // 居中显示日期
  OLED_ShowString((128 - date_len * 8) / 2, 20, date_str, OLED_8X16);

  // 显示星期和提示信息（优化字符串构建）
  char info_str[20];
  info_str[0] = day_str[0]; info_str[1] = day_str[1]; info_str[2] = day_str[2];
  info_str[3] = ' '; info_str[4] = ' ';
  const char* menu_text = "MENU:Sensor";
  for (int i = 0; i < 11; i++) {
    info_str[5 + i] = menu_text[i];
  }
  info_str[16] = '\0';
  OLED_ShowString(0, 40, info_str, OLED_8X16);

  // 移除闪烁的星号，保持界面简洁
}

/**
 * @brief 显示传感器数据界面
 */
void Display_Sensor_Interface(float temperature, float humidity, uint16_t tvoc, uint16_t eco2)
{
  // 完全安全的传感器数据显示，包含空气质量信息

  // 转换为整数，避免浮点数问题
  int temp_int = 0, humi_int = 0;

  // 安全的浮点数转换
  if (temperature >= -50.0f && temperature <= 100.0f) {
    temp_int = (int)(temperature * 10 + 0.5f);  // 四舍五入
  }
  if (humidity >= 0.0f && humidity <= 100.0f) {
    humi_int = (int)(humidity * 10 + 0.5f);     // 四舍五入
  }

  // 限制范围
  if (temp_int < -500) temp_int = 0;
  if (temp_int > 1000) temp_int = 0;
  if (humi_int < 0) humi_int = 0;
  if (humi_int > 1000) humi_int = 1000;

  // 检查空气质量数据
  if (tvoc > 60000) tvoc = 0;
  if (eco2 > 60000) eco2 = 0;

  // 显示标题
  OLED_ShowString(25, 0, "SENSORS", OLED_8X16);

  // 显示温度（安全的字符串构建）
  char temp_str[12] = "T:--.-C";
  if (temp_int >= 0) {
    temp_str[2] = '0' + (temp_int / 100) % 10;
    temp_str[3] = '0' + (temp_int / 10) % 10;
    temp_str[5] = '0' + temp_int % 10;
  }
  OLED_ShowString(0, 16, temp_str, OLED_8X16);

  // 显示湿度（安全的字符串构建）
  char humi_str[12] = "H:--.-%";
  if (humi_int >= 0) {
    humi_str[2] = '0' + (humi_int / 100) % 10;
    humi_str[3] = '0' + (humi_int / 10) % 10;
    humi_str[5] = '0' + humi_int % 10;
  }
  OLED_ShowString(64, 16, humi_str, OLED_8X16);

  // 显示空气质量信息
  if (tvoc > 0 || eco2 > 0) {
    // 显示TVOC（ppb单位）
    char tvoc_str[20] = "TVOC:----ppb";
    if (tvoc > 0 && tvoc < 10000) {
      tvoc_str[5] = '0' + (tvoc / 1000) % 10;
      tvoc_str[6] = '0' + (tvoc / 100) % 10;
      tvoc_str[7] = '0' + (tvoc / 10) % 10;
      tvoc_str[8] = '0' + tvoc % 10;
    }
    OLED_ShowString(0, 32, tvoc_str, OLED_8X16);

    // 显示CO2（ppm单位）
    char eco2_str[20] = "CO2:----ppm";
    if (eco2 > 0 && eco2 < 10000) {
      eco2_str[4] = '0' + (eco2 / 1000) % 10;
      eco2_str[5] = '0' + (eco2 / 100) % 10;
      eco2_str[6] = '0' + (eco2 / 10) % 10;
      eco2_str[7] = '0' + eco2 % 10;
    }
    OLED_ShowString(0, 48, eco2_str, OLED_8X16);
  } else {
    // 没有空气质量数据时显示提示
    OLED_ShowString(0, 32, "Air Quality", OLED_8X16);
    OLED_ShowString(0, 48, "Initializing", OLED_8X16);
  }
}

/**
 * @brief 显示闹钟设置界面
 */
void Display_Alarm_Interface(void)
{
  char alarm_str[20];

  // 显示标题（居中）
  OLED_ShowString(42, 0, "ALARM", OLED_8X16);

  // 显示闹钟时间（居中）
  snprintf(alarm_str, sizeof(alarm_str), "%02d:%02d", g_alarm.hour, g_alarm.minute);
  uint8_t alarm_len = strlen(alarm_str);
  OLED_ShowString((128 - alarm_len * 8) / 2, 20, alarm_str, OLED_8X16);

  // 显示状态（居中）
  if (g_alarm.enabled) {
    OLED_ShowString(56, 40, "ON", OLED_8X16);
  } else {
    OLED_ShowString(48, 40, "OFF", OLED_8X16);
  }

  // 显示操作提示
  OLED_ShowString(0, 56, "MENU:Back UP:Toggle", OLED_6X8);
}

/**
 * @brief 显示计时器界面
 */
void Display_Timer_Interface(void)
{
  char timer_str[20];
  unsigned long hours, minutes, seconds;

  // 显示标题（居中）
  OLED_ShowString(42, 0, "TIMER", OLED_8X16);

  // 计算小时、分钟和秒（支持更大时间范围）
  hours = g_timer.remaining_seconds / 3600;
  minutes = (g_timer.remaining_seconds % 3600) / 60;
  seconds = g_timer.remaining_seconds % 60;

  // 显示剩余时间（根据时间长度选择格式）
  if (hours > 0) {
    // 超过1小时：显示HH:MM格式
    snprintf(timer_str, sizeof(timer_str), "%02d:%02dh", (int)hours, (int)minutes);
  } else {
    // 小于1小时：显示MM:SS格式
    snprintf(timer_str, sizeof(timer_str), "%02d:%02d", (int)minutes, (int)seconds);
  }

  uint8_t timer_len = strlen(timer_str);
  OLED_ShowString((128 - timer_len * 8) / 2, 20, timer_str, OLED_8X16);

  // 显示状态（居中）
  if (g_timer.finished) {
    OLED_ShowString(24, 40, "FINISHED!", OLED_8X16);
  } else if (g_timer.running) {
    OLED_ShowString(32, 40, "RUNNING", OLED_8X16);
  } else {
    OLED_ShowString(40, 40, "PAUSED", OLED_8X16);
  }

  // 显示操作提示
  OLED_ShowString(0, 56, "UP:Start DOWN:Reset", OLED_6X8);
}

/**
 * @brief 显示秒表界面
 */
void Display_Stopwatch_Interface(void)
{
  char stopwatch_str[20];
  unsigned long total_ms, minutes, seconds, ms;

  // 显示标题
  OLED_ShowString(25, 0, "STOPWATCH", OLED_8X16);

  // 计算总时间
  total_ms = g_stopwatch.elapsed_ms;
  if (g_stopwatch.running) {
    total_ms += HAL_GetTick() - g_stopwatch.start_time;
  }

  // 转换为分:秒.毫秒格式
  minutes = total_ms / 60000;
  seconds = (total_ms % 60000) / 1000;
  ms = (total_ms % 1000) / 10;  // 显示百分之一秒

  // 显示时间
  snprintf(stopwatch_str, sizeof(stopwatch_str), "%02d:%02d.%02d", (int)minutes, (int)seconds, (int)ms);
  uint8_t stopwatch_len = strlen(stopwatch_str);
  OLED_ShowString((128 - stopwatch_len * 8) / 2, 20, stopwatch_str, OLED_8X16);

  // 显示状态
  if (g_stopwatch.running) {
    OLED_ShowString(35, 40, "RUNNING", OLED_8X16);
  } else {
    OLED_ShowString(38, 40, "PAUSED", OLED_8X16);
  }

  // 显示操作提示
  OLED_ShowString(0, 56, "UP:Start DOWN:Reset", OLED_6X8);
}

/**
 * @brief 更新计时器
 */
void Update_Timer(void)
{
  static unsigned long last_update = 0;
  unsigned long current_time = HAL_GetTick();

  if (g_timer.running && !g_timer.finished) {
    // 处理时间溢出情况
    unsigned long time_diff;
    if (current_time >= last_update) {
      time_diff = current_time - last_update;
    } else {
      // 处理溢出情况
      time_diff = (0xFFFFFFFF - last_update) + current_time + 1;
    }

    if (time_diff >= 1000) {  // 每秒更新
      if (g_timer.remaining_seconds > 0) {
        g_timer.remaining_seconds--;
        last_update = current_time;
      } else {
        g_timer.running = 0;
        g_timer.finished = 1;
        last_update = current_time;
        // 触发计时器结束蜂鸣器
        Buzzer_Start_Timer();
      }
    }
  } else {
    // 如果不在运行状态，更新时间基准
    last_update = current_time;
  }
}

/**
 * @brief 更新秒表
 */
void Update_Stopwatch(void)
{
  // 秒表更新在显示时实时计算，这里不需要额外处理
}

/**
 * @brief 检查闹钟
 */
void Check_Alarm(void)
{
  // 使用新的多闹钟检查函数
  Check_Multi_Alarms();
}

/**
 * @brief 进入设置模式
 */
void Enter_Setting_Mode(void)
{
  // 根据当前显示模式确定设置类型
  switch(g_display_mode) {
    case DISPLAY_MODE_CLOCK:
      g_setting_mode = SETTING_MODE_TIME;
      // 复制当前时间到临时变量
      g_temp_time = g_current_time;
      g_setting_item = TIME_ITEM_YEAR;
      break;

    case DISPLAY_MODE_ALARM:
      g_setting_mode = SETTING_MODE_MULTI_ALARM;
      // 复制当前显示的闹钟到临时变量
      g_editing_alarm_id = g_alarm_manager.current_alarm;
      g_temp_multi_alarm = g_alarm_manager.alarms[g_editing_alarm_id];
      g_setting_item = MULTI_ALARM_ITEM_HOUR;
      break;

    case DISPLAY_MODE_DATA_LOG:
      // 数据记录模式：无设置功能
      return;  // 直接返回，不进入设置模式

    case DISPLAY_MODE_TIMER:
      g_setting_mode = SETTING_MODE_TIMER;
      // 复制当前计时器到临时变量
      g_temp_timer.total_seconds = g_timer.total_seconds;
      g_temp_timer.remaining_seconds = g_timer.total_seconds;  // 重置为总时间
      g_setting_item = TIMER_ITEM_HOUR;
      break;

    default:
      return;  // 其他模式不支持设置
  }

  // 设置超时时间
  g_setting_timeout = HAL_GetTick() + SETTING_TIMEOUT_MS;
}

/**
 * @brief 切换到下一个设置项
 */
void Next_Setting_Item(void)
{
  switch(g_setting_mode) {
    case SETTING_MODE_TIME:
      g_setting_item = (g_setting_item + 1) % TIME_ITEM_COUNT;
      break;

    case SETTING_MODE_ALARM:
      g_setting_item = (g_setting_item + 1) % ALARM_ITEM_COUNT;
      break;

    case SETTING_MODE_MULTI_ALARM:
      g_setting_item = (g_setting_item + 1) % MULTI_ALARM_ITEM_COUNT;
      break;

    case SETTING_MODE_TIMER:
      g_setting_item = (g_setting_item + 1) % TIMER_ITEM_COUNT;
      break;

    default:
      break;
  }

  // 重置超时时间
  g_setting_timeout = HAL_GetTick() + SETTING_TIMEOUT_MS;
}

/**
 * @brief 增加当前设置项的值
 */
void Increase_Setting_Value(void)
{
  switch(g_setting_mode) {
    case SETTING_MODE_TIME:
      switch(g_setting_item) {
        case TIME_ITEM_YEAR:
          g_temp_time.year++;
          if (g_temp_time.year > 2030) g_temp_time.year = 2025;
          break;
        case TIME_ITEM_MONTH:
          g_temp_time.month++;
          if (g_temp_time.month > 12) g_temp_time.month = 1;
          break;
        case TIME_ITEM_DATE:
          g_temp_time.date++;
          if (g_temp_time.date > 31) g_temp_time.date = 1;
          break;
        case TIME_ITEM_HOUR:
          g_temp_time.hour++;
          if (g_temp_time.hour > 23) g_temp_time.hour = 0;
          break;
        case TIME_ITEM_MINUTE:
          g_temp_time.minute++;
          if (g_temp_time.minute > 59) g_temp_time.minute = 0;
          break;
      }
      break;

    case SETTING_MODE_ALARM:
      switch(g_setting_item) {
        case ALARM_ITEM_HOUR:
          g_temp_alarm.hour++;
          if (g_temp_alarm.hour > 23) g_temp_alarm.hour = 0;
          break;
        case ALARM_ITEM_MINUTE:
          g_temp_alarm.minute++;
          if (g_temp_alarm.minute > 59) g_temp_alarm.minute = 0;
          break;
        case ALARM_ITEM_ENABLE:
          g_temp_alarm.enabled = !g_temp_alarm.enabled;
          break;
      }
      break;

    case SETTING_MODE_MULTI_ALARM:
      switch(g_setting_item) {
        case MULTI_ALARM_ITEM_HOUR:
          g_temp_multi_alarm.hour++;
          if (g_temp_multi_alarm.hour > 23) g_temp_multi_alarm.hour = 0;
          break;
        case MULTI_ALARM_ITEM_MINUTE:
          g_temp_multi_alarm.minute++;
          if (g_temp_multi_alarm.minute > 59) g_temp_multi_alarm.minute = 0;
          break;
        case MULTI_ALARM_ITEM_ENABLED:
          g_temp_multi_alarm.enabled = !g_temp_multi_alarm.enabled;
          break;
        case MULTI_ALARM_ITEM_REPEAT:
          // 循环切换重复模式
          if (g_temp_multi_alarm.repeat_days == REPEAT_ONCE) {
            g_temp_multi_alarm.repeat_days = REPEAT_WEEKDAYS;
          } else if (g_temp_multi_alarm.repeat_days == REPEAT_WEEKDAYS) {
            g_temp_multi_alarm.repeat_days = REPEAT_WEEKEND;
          } else if (g_temp_multi_alarm.repeat_days == REPEAT_WEEKEND) {
            g_temp_multi_alarm.repeat_days = REPEAT_EVERYDAY;
          } else {
            g_temp_multi_alarm.repeat_days = REPEAT_ONCE;
          }
          break;
      }
      break;

    case SETTING_MODE_TIMER:
      {
        uint32_t hours = g_temp_timer.total_seconds / 3600;
        uint32_t minutes = (g_temp_timer.total_seconds % 3600) / 60;
        uint32_t seconds = g_temp_timer.total_seconds % 60;

        switch(g_setting_item) {
          case TIMER_ITEM_HOUR:
            hours++;
            if (hours > 23) hours = 0;
            break;
          case TIMER_ITEM_MINUTE:
            minutes++;
            if (minutes > 59) minutes = 0;
            break;
          case TIMER_ITEM_SECOND:
            seconds++;
            if (seconds > 59) seconds = 0;
            break;
        }

        g_temp_timer.total_seconds = hours * 3600 + minutes * 60 + seconds;
        g_temp_timer.remaining_seconds = g_temp_timer.total_seconds;
      }
      break;

    default:
      break;
  }

  // 重置超时时间
  g_setting_timeout = HAL_GetTick() + SETTING_TIMEOUT_MS;
}

/**
 * @brief 减少当前设置项的值
 */
void Decrease_Setting_Value(void)
{
  switch(g_setting_mode) {
    case SETTING_MODE_TIME:
      switch(g_setting_item) {
        case TIME_ITEM_YEAR:
          if (g_temp_time.year > 2025) {
            g_temp_time.year--;
          } else {
            g_temp_time.year = 2030;
          }
          break;
        case TIME_ITEM_MONTH:
          if (g_temp_time.month > 1) {
            g_temp_time.month--;
          } else {
            g_temp_time.month = 12;
          }
          break;
        case TIME_ITEM_DATE:
          if (g_temp_time.date > 1) {
            g_temp_time.date--;
          } else {
            g_temp_time.date = 31;
          }
          break;
        case TIME_ITEM_HOUR:
          if (g_temp_time.hour > 0) {
            g_temp_time.hour--;
          } else {
            g_temp_time.hour = 23;
          }
          break;
        case TIME_ITEM_MINUTE:
          if (g_temp_time.minute > 0) {
            g_temp_time.minute--;
          } else {
            g_temp_time.minute = 59;
          }
          break;
      }
      break;

    case SETTING_MODE_ALARM:
      switch(g_setting_item) {
        case ALARM_ITEM_HOUR:
          if (g_temp_alarm.hour > 0) {
            g_temp_alarm.hour--;
          } else {
            g_temp_alarm.hour = 23;
          }
          break;
        case ALARM_ITEM_MINUTE:
          if (g_temp_alarm.minute > 0) {
            g_temp_alarm.minute--;
          } else {
            g_temp_alarm.minute = 59;
          }
          break;
        case ALARM_ITEM_ENABLE:
          g_temp_alarm.enabled = !g_temp_alarm.enabled;
          break;
      }
      break;

    case SETTING_MODE_MULTI_ALARM:
      switch(g_setting_item) {
        case MULTI_ALARM_ITEM_HOUR:
          if (g_temp_multi_alarm.hour > 0) {
            g_temp_multi_alarm.hour--;
          } else {
            g_temp_multi_alarm.hour = 23;
          }
          break;
        case MULTI_ALARM_ITEM_MINUTE:
          if (g_temp_multi_alarm.minute > 0) {
            g_temp_multi_alarm.minute--;
          } else {
            g_temp_multi_alarm.minute = 59;
          }
          break;
        case MULTI_ALARM_ITEM_ENABLED:
          g_temp_multi_alarm.enabled = !g_temp_multi_alarm.enabled;
          break;
        case MULTI_ALARM_ITEM_REPEAT:
          // 反向循环切换重复模式
          if (g_temp_multi_alarm.repeat_days == REPEAT_ONCE) {
            g_temp_multi_alarm.repeat_days = REPEAT_EVERYDAY;
          } else if (g_temp_multi_alarm.repeat_days == REPEAT_EVERYDAY) {
            g_temp_multi_alarm.repeat_days = REPEAT_WEEKEND;
          } else if (g_temp_multi_alarm.repeat_days == REPEAT_WEEKEND) {
            g_temp_multi_alarm.repeat_days = REPEAT_WEEKDAYS;
          } else {
            g_temp_multi_alarm.repeat_days = REPEAT_ONCE;
          }
          break;
      }
      break;

    case SETTING_MODE_TIMER:
      {
        uint32_t hours = g_temp_timer.total_seconds / 3600;
        uint32_t minutes = (g_temp_timer.total_seconds % 3600) / 60;
        uint32_t seconds = g_temp_timer.total_seconds % 60;

        switch(g_setting_item) {
          case TIMER_ITEM_HOUR:
            if (hours > 0) {
              hours--;
            } else {
              hours = 23;
            }
            break;
          case TIMER_ITEM_MINUTE:
            if (minutes > 0) {
              minutes--;
            } else {
              minutes = 59;
            }
            break;
          case TIMER_ITEM_SECOND:
            if (seconds > 0) {
              seconds--;
            } else {
              seconds = 59;
            }
            break;
        }

        g_temp_timer.total_seconds = hours * 3600 + minutes * 60 + seconds;
        g_temp_timer.remaining_seconds = g_temp_timer.total_seconds;
      }
      break;

    default:
      break;
  }

  // 重置超时时间
  g_setting_timeout = HAL_GetTick() + SETTING_TIMEOUT_MS;
}

/**
 * @brief 保存设置并退出设置模式
 */
void Save_And_Exit_Setting(void)
{
  switch(g_setting_mode) {
    case SETTING_MODE_TIME:
      // 保存时间到DS1302
      DS1302_SetTime(&g_temp_time);
      g_current_time = g_temp_time;
      break;

    case SETTING_MODE_ALARM:
      // 保存闹钟设置
      g_alarm = g_temp_alarm;
      g_alarm.triggered = 0;  // 重置触发状态
      break;

    case SETTING_MODE_MULTI_ALARM:
      // 保存多闹钟设置
      g_alarm_manager.alarms[g_editing_alarm_id] = g_temp_multi_alarm;
      g_alarm_manager.alarms[g_editing_alarm_id].triggered = 0;  // 重置触发状态
      break;

    case SETTING_MODE_TIMER:
      // 保存计时器设置
      g_timer.total_seconds = g_temp_timer.total_seconds;
      g_timer.remaining_seconds = g_temp_timer.total_seconds;
      g_timer.running = 0;
      g_timer.finished = 0;
      break;

    default:
      break;
  }

  // 退出设置模式
  g_setting_mode = SETTING_MODE_NONE;
  g_setting_item = 0;
}

/**
 * @brief 显示时间设置界面
 */
void Display_Time_Setting(void)
{
  char date_str[20], time_str[20];

  // 显示标题（居中）
  OLED_ShowString(32, 0, "SET TIME", OLED_8X16);

  // 显示日期，当前选中项用[]标识
  if (g_setting_item == TIME_ITEM_YEAR) {
    snprintf(date_str, sizeof(date_str), "[%04d]-%02d-%02d",
             g_temp_time.year, g_temp_time.month, g_temp_time.date);
  } else if (g_setting_item == TIME_ITEM_MONTH) {
    snprintf(date_str, sizeof(date_str), "%04d-[%02d]-%02d",
             g_temp_time.year, g_temp_time.month, g_temp_time.date);
  } else if (g_setting_item == TIME_ITEM_DATE) {
    snprintf(date_str, sizeof(date_str), "%04d-%02d-[%02d]",
             g_temp_time.year, g_temp_time.month, g_temp_time.date);
  } else {
    snprintf(date_str, sizeof(date_str), "%04d-%02d-%02d",
             g_temp_time.year, g_temp_time.month, g_temp_time.date);
  }
  // 日期居中显示
  uint8_t date_len = strlen(date_str);
  OLED_ShowString((128 - date_len * 8) / 2, 20, date_str, OLED_8X16);

  // 显示时间，当前选中项用[]标识
  if (g_setting_item == TIME_ITEM_HOUR) {
    snprintf(time_str, sizeof(time_str), "[%02d]:%02d",
             g_temp_time.hour, g_temp_time.minute);
  } else if (g_setting_item == TIME_ITEM_MINUTE) {
    snprintf(time_str, sizeof(time_str), "%02d:[%02d]",
             g_temp_time.hour, g_temp_time.minute);
  } else {
    snprintf(time_str, sizeof(time_str), "%02d:%02d",
             g_temp_time.hour, g_temp_time.minute);
  }
  // 时间居中显示
  uint8_t time_len = strlen(time_str);
  OLED_ShowString((128 - time_len * 8) / 2, 40, time_str, OLED_8X16);

  // 显示操作提示
  OLED_ShowString(0, 56, "SET:Next UP:+ DOWN:-", OLED_6X8);
}

/**
 * @brief 显示闹钟设置界面
 */
void Display_Alarm_Setting(void)
{
  char alarm_str[20];

  // 显示标题（居中）
  OLED_ShowString(24, 0, "SET ALARM", OLED_8X16);

  // 显示闹钟时间，当前选中项用[]标识
  if (g_setting_item == ALARM_ITEM_HOUR) {
    snprintf(alarm_str, sizeof(alarm_str), "[%02d]:%02d",
             g_temp_alarm.hour, g_temp_alarm.minute);
  } else if (g_setting_item == ALARM_ITEM_MINUTE) {
    snprintf(alarm_str, sizeof(alarm_str), "%02d:[%02d]",
             g_temp_alarm.hour, g_temp_alarm.minute);
  } else {
    snprintf(alarm_str, sizeof(alarm_str), "%02d:%02d",
             g_temp_alarm.hour, g_temp_alarm.minute);
  }
  // 闹钟时间居中显示
  uint8_t alarm_len = strlen(alarm_str);
  OLED_ShowString((128 - alarm_len * 8) / 2, 20, alarm_str, OLED_8X16);

  // 显示开关状态，当前选中项用[]标识（居中）
  if (g_setting_item == ALARM_ITEM_ENABLE) {
    if (g_temp_alarm.enabled) {
      OLED_ShowString(48, 40, "[ON]", OLED_8X16);
    } else {
      OLED_ShowString(44, 40, "[OFF]", OLED_8X16);
    }
  } else {
    if (g_temp_alarm.enabled) {
      OLED_ShowString(56, 40, "ON", OLED_8X16);
    } else {
      OLED_ShowString(48, 40, "OFF", OLED_8X16);
    }
  }

  // 显示操作提示
  OLED_ShowString(0, 56, "SET:Next UP:+ DOWN:-", OLED_6X8);
}

/**
 * @brief 显示计时器设置界面
 */
void Display_Timer_Setting(void)
{
  char timer_str[20];
  uint32_t hours, minutes, seconds;

  // 显示标题（居中）
  OLED_ShowString(24, 0, "SET TIMER", OLED_8X16);

  // 计算时分秒
  hours = g_temp_timer.total_seconds / 3600;
  minutes = (g_temp_timer.total_seconds % 3600) / 60;
  seconds = g_temp_timer.total_seconds % 60;

  // 显示时间，当前选中项用[]标识
  if (g_setting_item == TIMER_ITEM_HOUR) {
    snprintf(timer_str, sizeof(timer_str), "[%02d]:%02d:%02d",
             (int)hours, (int)minutes, (int)seconds);
  } else if (g_setting_item == TIMER_ITEM_MINUTE) {
    snprintf(timer_str, sizeof(timer_str), "%02d:[%02d]:%02d",
             (int)hours, (int)minutes, (int)seconds);
  } else if (g_setting_item == TIMER_ITEM_SECOND) {
    snprintf(timer_str, sizeof(timer_str), "%02d:%02d:[%02d]",
             (int)hours, (int)minutes, (int)seconds);
  } else {
    snprintf(timer_str, sizeof(timer_str), "%02d:%02d:%02d",
             (int)hours, (int)minutes, (int)seconds);
  }
  // 计时器时间居中显示
  uint8_t timer_len = strlen(timer_str);
  OLED_ShowString((128 - timer_len * 8) / 2, 20, timer_str, OLED_8X16);

  // 显示说明（居中）
  OLED_ShowString(16, 40, "Custom Timer", OLED_8X16);

  // 显示操作提示
  OLED_ShowString(0, 56, "SET:Next UP:+ DOWN:-", OLED_6X8);
}

/**
 * @brief 开始闹钟响铃
 */
void Buzzer_Start_Alarm(void)
{
  g_buzzer.mode = BUZZER_ALARM;
  g_buzzer.active = 1;
  g_buzzer.start_time = HAL_GetTick();
  g_buzzer.duration = 30000;  // 30秒
  g_buzzer.pattern_time = 0;
  g_buzzer.pattern_state = 0;
}

/**
 * @brief 开始计时器响铃
 */
void Buzzer_Start_Timer(void)
{
  g_buzzer.mode = BUZZER_TIMER;
  g_buzzer.active = 1;
  g_buzzer.start_time = HAL_GetTick();
  g_buzzer.duration = 10000;  // 10秒
  g_buzzer.pattern_time = 0;
  g_buzzer.pattern_state = 0;
}

/**
 * @brief 按键音效 - 超简化实现
 */
void Buzzer_Key_Beep(void)
{
  // 如果当前正在播放按键音效，不重复播放
  if (g_buzzer.active && g_buzzer.mode == BUZZER_KEY_BEEP) {
    return;
  }

  // 停止其他可能的蜂鸣器活动
  if (g_buzzer.active && (g_buzzer.mode == BUZZER_ALARM || g_buzzer.mode == BUZZER_TIMER)) {
    // 如果是闹钟或计时器在响，不播放按键音效
    return;
  }

  // 设置按键音效状态 - 使用更短的时间
  g_buzzer.mode = BUZZER_KEY_BEEP;
  g_buzzer.active = 1;
  g_buzzer.start_time = HAL_GetTick();
  g_buzzer.duration = 30;   // 减少到30ms，更短促
  g_buzzer.pattern_time = 0;
  g_buzzer.pattern_state = 1;
  BUZZER_ON();
}

/**
 * @brief 停止蜂鸣器
 */
void Buzzer_Stop(void)
{
  g_buzzer.mode = BUZZER_OFF;
  g_buzzer.active = 0;
  g_buzzer.pattern_state = 0;
  BUZZER_OFF();
}

/**
 * @brief 统一的按键音效处理 - 超简化测试版本
 */
void Handle_Key_Beep(void)
{
  // 超简化版本：直接控制蜂鸣器，绕过所有状态管理
  static uint32_t last_beep_time = 0;
  uint32_t current_time = HAL_GetTick();

  // 防止过于频繁的音效（最少间隔100ms）
  if (current_time - last_beep_time < 100) {
    return;
  }

  // 如果蜂鸣器正在响铃（闹钟或计时器），停止响铃
  if (g_buzzer.active && (g_buzzer.mode == BUZZER_ALARM || g_buzzer.mode == BUZZER_TIMER)) {
    Buzzer_Stop();
    return;
  }

  // 直接控制蜂鸣器：立即响30ms
  BUZZER_ON();

  // 记录时间，用于后续关闭
  last_beep_time = current_time;

  // 设置一个简单的标志，让Buzzer_Update在30ms后关闭
  g_buzzer.mode = BUZZER_KEY_BEEP;
  g_buzzer.active = 1;
  g_buzzer.start_time = current_time;
  g_buzzer.duration = 30;
}

/**
 * @brief 更新蜂鸣器状态（在主循环中调用）
 */
void Buzzer_Update(void)
{
  if (!g_buzzer.active) {
    return;  // 蜂鸣器未激活，直接返回
  }

  uint32_t current_time = HAL_GetTick();
  uint32_t elapsed_time = current_time - g_buzzer.start_time;

  // 检查总持续时间 - 对于按键音效，这是主要的停止机制
  if (elapsed_time >= g_buzzer.duration) {
    Buzzer_Stop();
    return;
  }

  // 根据模式处理不同的响铃模式
  switch(g_buzzer.mode) {
    case BUZZER_ALARM:
      // 闹钟模式：响1秒，停1秒
      {
        uint32_t pattern_elapsed = current_time - (g_buzzer.start_time + g_buzzer.pattern_time);
        if (g_buzzer.pattern_state == 0) {
          // 当前是停止状态，检查是否该开始响
          if (pattern_elapsed >= 1000) {  // 停止1秒后开始响
            g_buzzer.pattern_state = 1;
            g_buzzer.pattern_time = elapsed_time;
            BUZZER_ON();
          }
        } else {
          // 当前是响铃状态，检查是否该停止
          if (pattern_elapsed >= 1000) {  // 响1秒后停止
            g_buzzer.pattern_state = 0;
            g_buzzer.pattern_time = elapsed_time;
            BUZZER_OFF();
          }
        }
      }
      break;

    case BUZZER_TIMER:
      // 计时器模式：响0.3秒，停0.3秒
      {
        uint32_t pattern_elapsed = current_time - (g_buzzer.start_time + g_buzzer.pattern_time);
        if (g_buzzer.pattern_state == 0) {
          // 当前是停止状态，检查是否该开始响
          if (pattern_elapsed >= 300) {  // 停止0.3秒后开始响
            g_buzzer.pattern_state = 1;
            g_buzzer.pattern_time = elapsed_time;
            BUZZER_ON();
          }
        } else {
          // 当前是响铃状态，检查是否该停止
          if (pattern_elapsed >= 300) {  // 响0.3秒后停止
            g_buzzer.pattern_state = 0;
            g_buzzer.pattern_time = elapsed_time;
            BUZZER_OFF();
          }
        }
      }
      break;

    case BUZZER_KEY_BEEP:
      // 按键音效：简单的50ms响铃，时间到立即停止
      // 不需要复杂的状态管理，直接检查时间
      break;

    default:
      Buzzer_Stop();
      break;
  }
}

/**
 * @brief GPIO中断回调函数 - 立即响应按键
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  // 立即记录按键时间
  uint32_t current_time = HAL_GetTick();

  // 根据具体按键设置对应的中断标志
  switch (GPIO_Pin) {
    case KEY_UP_Pin:
      g_key_up_interrupt = 1;
      g_key_up_interrupt_time = current_time;
      break;
    case KEY_DOWN_Pin:
      g_key_down_interrupt = 1;
      g_key_down_interrupt_time = current_time;
      break;
    case KEY_MENU_Pin:
      g_key_menu_interrupt = 1;
      g_key_menu_interrupt_time = current_time;
      break;
    case KEY_SET_Pin:
      g_key_set_interrupt = 1;
      g_key_set_interrupt_time = current_time;
      break;
    default:
      return;  // 未知按键，直接返回
  }

  // 检查是否有响铃需要停止
  if (g_buzzer.active && (g_buzzer.mode == BUZZER_ALARM || g_buzzer.mode == BUZZER_TIMER)) {
    Buzzer_Stop();  // 停止闹钟或计时器响铃
  }

  // 音效已禁用 - 按用户要求去掉按键音效
  // Handle_Key_Beep();

  // 设置通用中断标志
  g_key_interrupt_flag = 1;
  g_key_press_time = current_time;
}

/**
 * @brief 多闹钟管理函数实现
 */

/**
 * @brief 添加新闹钟
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0-59)
 * @param label 闹钟标签
 * @return 闹钟ID (0-4)，如果失败返回0xFF
 */
uint8_t Add_New_Alarm(uint8_t hour, uint8_t minute, const char* label)
{
    for (int i = 0; i < 5; i++) {
        if (!g_alarm_manager.alarms[i].enabled) {
            g_alarm_manager.alarms[i].hour = hour;
            g_alarm_manager.alarms[i].minute = minute;
            g_alarm_manager.alarms[i].enabled = 1;
            g_alarm_manager.alarms[i].triggered = 0;
            g_alarm_manager.alarms[i].repeat_days = REPEAT_ONCE;
            strncpy(g_alarm_manager.alarms[i].label, label, 7);
            g_alarm_manager.alarms[i].label[7] = '\0';
            g_alarm_manager.total_alarms++;
            return i;  // 返回闹钟ID
        }
    }
    return 0xFF;  // 闹钟已满
}

/**
 * @brief 删除闹钟
 * @param alarm_id 闹钟ID (0-4)
 */
void Delete_Alarm(uint8_t alarm_id)
{
    if (alarm_id < 5 && g_alarm_manager.alarms[alarm_id].enabled) {
        memset(&g_alarm_manager.alarms[alarm_id], 0, sizeof(multi_alarm_t));
        strcpy(g_alarm_manager.alarms[alarm_id].label, "Alarm1");
        g_alarm_manager.total_alarms--;
    }
}

/**
 * @brief 获取下一个将要响铃的闹钟
 * @return 闹钟ID，如果没有返回0xFF
 */
uint8_t Get_Next_Alarm(void)
{
    uint32_t current_minutes = g_current_time.hour * 60 + g_current_time.minute;
    uint32_t next_minutes = 24 * 60;  // 明天的0点
    uint8_t next_alarm_id = 0xFF;

    for (int i = 0; i < 5; i++) {
        if (!g_alarm_manager.alarms[i].enabled) continue;

        uint32_t alarm_minutes = g_alarm_manager.alarms[i].hour * 60 +
                                g_alarm_manager.alarms[i].minute;

        if (alarm_minutes > current_minutes && alarm_minutes < next_minutes) {
            next_minutes = alarm_minutes;
            next_alarm_id = i;
        }
    }

    return next_alarm_id;
}

/**
 * @brief 检查闹钟重复设置
 * @param alarm 闹钟指针
 * @param current_day 当前星期 (1=周一, 7=周日)
 * @return 1=应该响铃, 0=不响铃
 */
uint8_t Check_Alarm_Repeat(multi_alarm_t *alarm, uint8_t current_day)
{
    // 如果没有设置重复，只响一次
    if (alarm->repeat_days == REPEAT_ONCE) {
        return 1;
    }

    // 检查当前星期是否在重复设置中
    // current_day: 1=周一, 7=周日
    uint8_t day_bit = 1 << (current_day - 1);
    return (alarm->repeat_days & day_bit) != 0;
}

/**
 * @brief 检查所有闹钟
 */
void Check_Multi_Alarms(void)
{
    for (int i = 0; i < 5; i++) {
        multi_alarm_t *alarm = &g_alarm_manager.alarms[i];

        if (!alarm->enabled || alarm->triggered) {
            continue;
        }

        // 检查时间匹配
        if (g_current_time.hour == alarm->hour &&
            g_current_time.minute == alarm->minute) {

            // 检查重复日期
            if (Check_Alarm_Repeat(alarm, g_current_time.day)) {
                alarm->triggered = 1;
                Buzzer_Start_Alarm();  // 启动响铃

                // 如果是一次性闹钟，响铃后自动禁用
                if (alarm->repeat_days == REPEAT_ONCE) {
                    alarm->enabled = 0;
                }
            }
        }

        // 重置触发状态
        if (alarm->triggered && g_current_time.minute != alarm->minute) {
            alarm->triggered = 0;
        }
    }
}

/**
 * @brief 显示重复模式
 * @param repeat_days 重复日期位掩码
 * @param buffer 输出缓冲区
 */
void Display_Repeat_Pattern(uint8_t repeat_days, char* buffer)
{
    if (repeat_days == REPEAT_ONCE) {
        strcpy(buffer, "Once");
    } else if (repeat_days == REPEAT_WEEKDAYS) {
        strcpy(buffer, "Mon-Fri");
    } else if (repeat_days == REPEAT_WEEKEND) {
        strcpy(buffer, "Sat-Sun");  // 缩短显示
    } else if (repeat_days == REPEAT_EVERYDAY) {
        strcpy(buffer, "Daily");    // 缩短显示
    } else {
        // 自定义模式，显示具体日期缩写
        buffer[0] = '\0';
        if (repeat_days & REPEAT_MONDAY) strcat(buffer, "M");
        if (repeat_days & REPEAT_TUESDAY) strcat(buffer, "T");
        if (repeat_days & REPEAT_WEDNESDAY) strcat(buffer, "W");
        if (repeat_days & REPEAT_THURSDAY) strcat(buffer, "T");
        if (repeat_days & REPEAT_FRIDAY) strcat(buffer, "F");
        if (repeat_days & REPEAT_SATURDAY) strcat(buffer, "S");
        if (repeat_days & REPEAT_SUNDAY) strcat(buffer, "S");
    }
}

/**
 * @brief 显示简化的闹钟界面（适配0.96寸屏幕）
 */
void Display_Alarm_Simple(void)
{
    char line1[20], line2[20], line3[20], line4[20];
    char repeat_str[12];

    // 获取当前显示的闹钟
    multi_alarm_t *current_alarm = &g_alarm_manager.alarms[g_alarm_manager.current_alarm];

    // 标题：显示闹钟编号
    snprintf(line1, sizeof(line1), "  ALARM [%d]", g_alarm_manager.current_alarm + 1);

    // 闹钟时间和状态
    if (current_alarm->enabled || g_alarm_manager.current_alarm == 0) {
        snprintf(line2, sizeof(line2), "  %02d:%02d  %s",
                 current_alarm->hour, current_alarm->minute,
                 current_alarm->enabled ? "ON " : "OFF");

        // 重复模式（简化显示）
        Display_Repeat_Pattern(current_alarm->repeat_days, repeat_str);
        snprintf(line3, sizeof(line3), "  %s", repeat_str);
    } else {
        strcpy(line2, "  --:--  OFF");
        strcpy(line3, "  Not Set");
    }

    // 操作提示（简化）
    strcpy(line4, " UP/DOWN:Switch SET:Edit");

    // 显示
    OLED_Clear();
    OLED_ShowString(0, 0, line1, OLED_8X16);
    OLED_ShowString(0, 16, line2, OLED_8X16);
    OLED_ShowString(0, 32, line3, OLED_8X16);
    OLED_ShowString(0, 48, line4, OLED_8X16);
}

/**
 * @brief 显示多闹钟设置界面
 */
void Display_Multi_Alarm_Setting(void)
{
    char line1[20], line2[20], line3[20], line4[20];
    char repeat_str[12];

    // 标题：显示正在编辑的闹钟编号
    snprintf(line1, sizeof(line1), " EDIT [%d]", g_editing_alarm_id + 1);

    // 显示设置项
    switch(g_setting_item) {
        case MULTI_ALARM_ITEM_HOUR:
            snprintf(line2, sizeof(line2), " [%02d]:%02d",
                     g_temp_multi_alarm.hour, g_temp_multi_alarm.minute);
            break;
        case MULTI_ALARM_ITEM_MINUTE:
            snprintf(line2, sizeof(line2), " %02d:[%02d]",
                     g_temp_multi_alarm.hour, g_temp_multi_alarm.minute);
            break;
        case MULTI_ALARM_ITEM_ENABLED:
            snprintf(line2, sizeof(line2), " %02d:%02d",
                     g_temp_multi_alarm.hour, g_temp_multi_alarm.minute);
            break;
        case MULTI_ALARM_ITEM_REPEAT:
            snprintf(line2, sizeof(line2), " %02d:%02d",
                     g_temp_multi_alarm.hour, g_temp_multi_alarm.minute);
            break;
    }

    // 显示状态
    if (g_setting_item == MULTI_ALARM_ITEM_ENABLED) {
        snprintf(line3, sizeof(line3), " [%s]",
                 g_temp_multi_alarm.enabled ? "ON " : "OFF");
    } else {
        snprintf(line3, sizeof(line3), " %s",
                 g_temp_multi_alarm.enabled ? "ON " : "OFF");
    }

    // 重复模式
    Display_Repeat_Pattern(g_temp_multi_alarm.repeat_days, repeat_str);
    if (g_setting_item == MULTI_ALARM_ITEM_REPEAT) {
        snprintf(line4, sizeof(line4), " [%s]", repeat_str);
    } else {
        snprintf(line4, sizeof(line4), " %s", repeat_str);
    }

    // 显示
    OLED_Clear();
    OLED_ShowString(0, 0, line1, OLED_8X16);
    OLED_ShowString(0, 16, line2, OLED_8X16);
    OLED_ShowString(0, 32, line3, OLED_8X16);
    OLED_ShowString(0, 48, line4, OLED_8X16);
}

/**
 * @brief 数据记录功能实现（优化为0.96寸屏幕）
 */

/**
 * @brief 记录传感器数据（每10分钟一次）
 */
void Record_Sensor_Data(void)
{
    uint32_t current_time = HAL_GetTick();

    // 每10分钟记录一次 (600000ms)
    if (current_time - g_data_logger.last_record_time >= 600000) {

        // 创建新的数据点
        data_point_t new_point;
        new_point.hour = g_current_time.hour;
        new_point.minute = g_current_time.minute;
        new_point.temperature = (int8_t)g_current_temperature;  // 取整数部分
        new_point.humidity = (uint8_t)g_current_humidity;       // 取整数部分
        new_point.co2 = g_current_co2;

        // 存储数据点（循环缓冲区）
        g_data_logger.points[g_data_logger.current_index] = new_point;

        // 更新索引
        g_data_logger.current_index = (g_data_logger.current_index + 1) % 144;

        // 更新总数
        if (g_data_logger.total_points < 144) {
            g_data_logger.total_points++;
        }

        // 更新统计数据
        Update_Daily_Stats(&new_point);

        // 更新记录时间
        g_data_logger.last_record_time = current_time;
    }
}

/**
 * @brief 更新每日统计数据
 */
void Update_Daily_Stats(data_point_t* point)
{
    // 如果是第一个数据点，初始化统计
    if (g_data_logger.total_points == 1) {
        g_daily_stats.temp_max = g_daily_stats.temp_min = point->temperature;
        g_daily_stats.humi_max = g_daily_stats.humi_min = point->humidity;
        g_daily_stats.temp_max_hour = g_daily_stats.temp_min_hour = point->hour;
        g_daily_stats.temp_max_minute = g_daily_stats.temp_min_minute = point->minute;
        return;
    }

    // 更新温度统计
    if (point->temperature > g_daily_stats.temp_max) {
        g_daily_stats.temp_max = point->temperature;
        g_daily_stats.temp_max_hour = point->hour;
        g_daily_stats.temp_max_minute = point->minute;
    }
    if (point->temperature < g_daily_stats.temp_min) {
        g_daily_stats.temp_min = point->temperature;
        g_daily_stats.temp_min_hour = point->hour;
        g_daily_stats.temp_min_minute = point->minute;
    }

    // 更新湿度统计
    if (point->humidity > g_daily_stats.humi_max) {
        g_daily_stats.humi_max = point->humidity;
    }
    if (point->humidity < g_daily_stats.humi_min) {
        g_daily_stats.humi_min = point->humidity;
    }
}

/**
 * @brief 重置每日统计（新的一天开始时调用）
 */
void Reset_Daily_Stats(void)
{
    memset(&g_daily_stats, 0, sizeof(daily_stats_t));
    g_data_logger.total_points = 0;
    g_data_logger.current_index = 0;
}

/**
 * @brief 显示数据记录界面（适配0.96寸屏幕）
 */
void Display_Data_Log(void)
{
    char line1[17], line2[17], line3[17], line4[17];

    switch (g_data_view_mode) {
        case 0: // 数据概览
            strcpy(line1, "  DATA LOG");

            if (g_data_logger.total_points > 0) {
                // 显示当前数据（去掉°符号）
                snprintf(line2, sizeof(line2), " Now:%dC %d%%",
                         (int)g_current_temperature, (int)g_current_humidity);

                // 显示今日最高/最低
                snprintf(line3, sizeof(line3), " Max:%dC %d%%",
                         g_daily_stats.temp_max, g_daily_stats.humi_max);
                snprintf(line4, sizeof(line4), " Min:%dC %d%%",
                         g_daily_stats.temp_min, g_daily_stats.humi_min);
            } else {
                strcpy(line2, " No Data Yet");
                strcpy(line3, " Recording...");
                strcpy(line4, " UP/DOWN:Switch");
            }
            break;

        case 1: // 优化的趋势显示（适配0.96寸屏幕）
            strcpy(line1, " TREND");

            if (g_data_logger.total_points >= 3) {
                // 显示最近5个温度值的简洁趋势
                char temp_sequence[16];
                int sequence_len = (g_data_logger.total_points >= 5) ? 5 : g_data_logger.total_points;

                // 构建温度序列字符串
                temp_sequence[0] = '\0';
                for (int i = 0; i < sequence_len; i++) {
                    int idx = (g_data_logger.current_index - sequence_len + i + 144) % 144;
                    int temp = g_data_logger.points[idx].temperature;

                    if (i == 0) {
                        snprintf(temp_sequence, sizeof(temp_sequence), " %d", temp);
                    } else {
                        char temp_str[16];
                        snprintf(temp_str, sizeof(temp_str), "%s>%d", temp_sequence, temp);
                        strcpy(temp_sequence, temp_str);
                    }
                }
                strcat(temp_sequence, "C");
                strcpy(line2, temp_sequence);

                // 计算总体变化和趋势
                int latest_idx = (g_data_logger.current_index - 1 + 144) % 144;
                int earlier_idx = (g_data_logger.current_index - sequence_len + 144) % 144;

                int latest_temp = g_data_logger.points[latest_idx].temperature;
                int earlier_temp = g_data_logger.points[earlier_idx].temperature;
                int temp_change = latest_temp - earlier_temp;

                // 优化的分级算法：基于变化率和绝对变化
                char trend_status[16];
                if (temp_change >= 3) {
                    strcpy(trend_status, " Rising Fast");
                } else if (temp_change >= 1) {
                    strcpy(trend_status, " Rising");
                } else if (temp_change <= -3) {
                    strcpy(trend_status, " Falling Fast");
                } else if (temp_change <= -1) {
                    strcpy(trend_status, " Falling");
                } else {
                    strcpy(trend_status, " Stable");
                }
                strcpy(line3, trend_status);

                // 显示变化量
                if (temp_change != 0) {
                    snprintf(line4, sizeof(line4), " Change: %+dC", temp_change);
                } else {
                    strcpy(line4, " No Change");
                }
            } else {
                strcpy(line2, " Need 3+ Points");
                strcpy(line3, " Recording...");
                strcpy(line4, " UP/DOWN:Switch");
            }
            break;

        case 2: // 详细统计（简化显示）
            strcpy(line1, " STATS");

            if (g_data_logger.total_points > 0) {
                snprintf(line2, sizeof(line2), " Data:%d/144", g_data_logger.total_points);
                snprintf(line3, sizeof(line3), " Max:%02d:%02d %dC",
                         g_daily_stats.temp_max_hour, g_daily_stats.temp_max_minute, g_daily_stats.temp_max);
                snprintf(line4, sizeof(line4), " Min:%02d:%02d %dC",
                         g_daily_stats.temp_min_hour, g_daily_stats.temp_min_minute, g_daily_stats.temp_min);
            } else {
                strcpy(line2, " No Data");
                strcpy(line3, " Available");
                strcpy(line4, " UP/DOWN:Switch");
            }
            break;
    }

    // 显示
    OLED_Clear();
    OLED_ShowString(0, 0, line1, OLED_8X16);
    OLED_ShowString(0, 16, line2, OLED_8X16);
    OLED_ShowString(0, 32, line3, OLED_8X16);
    OLED_ShowString(0, 48, line4, OLED_8X16);
}

/**
 * @brief 显示天气界面
 */
// 临时注释天气显示函数
/*
void Display_Weather_Interface(void)
{
    char line1[20], line2[20], line3[20], line4[20];
    unsigned long elapsed;

    if (g_weather_data_valid && g_weather_data.data_valid) {
        // 显示天气数据
        snprintf(line1, sizeof(line1), " %s", g_weather_data.city_name);

        // 温度和天气状态
        snprintf(line2, sizeof(line2), " %.1fC %c",
                 g_weather_data.temperature,
                 Weather_StatusToIcon(g_weather_data.status));

        // 湿度和气压
        snprintf(line3, sizeof(line3), " H:%.0f%% P:%.0fhPa",
                 g_weather_data.humidity,
                 g_weather_data.pressure);

        // 更新时间
        elapsed = (HAL_GetTick() - g_last_weather_update) / 60000; // 分钟
        if (elapsed < 60) {
            snprintf(line4, sizeof(line4), " Updated %dmin ago", (int)elapsed);
        } else {
            snprintf(line4, sizeof(line4), " Updated %dh ago", (int)(elapsed / 60));
        }
    } else {
        // 无天气数据
        strcpy(line1, " WEATHER");
        strcpy(line2, " Connecting...");
        strcpy(line3, " Please Wait");
        strcpy(line4, " UP/DOWN:Switch");
    }

    // 显示
    OLED_Clear();
    OLED_ShowString(0, 0, line1, OLED_8X16);
    OLED_ShowString(0, 16, line2, OLED_8X16);
    OLED_ShowString(0, 32, line3, OLED_8X16);
    OLED_ShowString(0, 48, line4, OLED_8X16);
}
*/

/* USER CODE END Application */

