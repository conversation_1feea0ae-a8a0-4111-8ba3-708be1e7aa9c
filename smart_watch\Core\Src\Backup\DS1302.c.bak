/**
 * @file DS1302.c
 * @brief DS1302实时时钟驱动程序
 * <AUTHOR> Agent
 * @date 2025-07-27
 */

#include "DS1302.h"
#include <string.h>

/* 私有函数声明 */
static void DS1302_Delay_us(uint32_t us);
static void DS1302_WriteByte(uint8_t data);
static uint8_t DS1302_ReadByte(void);

/**
 * @brief 微秒级延时函数
 * @param us 延时微秒数
 */
static void DS1302_Delay_us(uint32_t us)
{
    // 简单的延时循环，根据系统时钟调整
    // 假设系统时钟72MHz，每个循环约1us
    volatile uint32_t count = us * 72;
    while(count--);
}

/**
 * @brief 设置DAT引脚为输出模式
 */
void DS1302_DAT_Output(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = DS1302_DAT_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(DS1302_DAT_PORT, &GPIO_InitStruct);
}

/**
 * @brief 设置DAT引脚为输入模式
 */
void DS1302_DAT_Input(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = DS1302_DAT_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(DS1302_DAT_PORT, &GPIO_InitStruct);
}

/**
 * @brief 向DS1302写入一个字节
 * @param data 要写入的数据
 */
static void DS1302_WriteByte(uint8_t data)
{
    uint8_t i;
    
    DS1302_DAT_Output();  // 设置为输出模式
    
    for(i = 0; i < 8; i++)
    {
        DS1302_CLK_LOW();
        DS1302_Delay_us(1);
        
        if(data & 0x01)
        {
            DS1302_DAT_HIGH();
        }
        else
        {
            DS1302_DAT_LOW();
        }
        
        DS1302_Delay_us(1);
        DS1302_CLK_HIGH();
        DS1302_Delay_us(1);
        
        data >>= 1;
    }
}

/**
 * @brief 从DS1302读取一个字节
 * @retval 读取的数据
 */
static uint8_t DS1302_ReadByte(void)
{
    uint8_t i, data = 0;
    
    DS1302_DAT_Input();  // 设置为输入模式
    
    for(i = 0; i < 8; i++)
    {
        data >>= 1;
        
        DS1302_CLK_LOW();
        DS1302_Delay_us(1);
        DS1302_CLK_HIGH();
        DS1302_Delay_us(1);
        
        if(DS1302_DAT_READ())
        {
            data |= 0x80;
        }
    }
    
    return data;
}

/**
 * @brief BCD转十进制
 * @param bcd BCD码
 * @retval 十进制数
 */
uint8_t DS1302_BCD2DEC(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/**
 * @brief 十进制转BCD
 * @param dec 十进制数
 * @retval BCD码
 */
uint8_t DS1302_DEC2BCD(uint8_t dec)
{
    return ((dec / 10) << 4) + (dec % 10);
}

/**
 * @brief 写入单个寄存器
 * @param reg 寄存器地址
 * @param data 数据
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_WriteReg(uint8_t reg, uint8_t data)
{
    DS1302_RST_LOW();
    DS1302_CLK_LOW();
    DS1302_Delay_us(2);
    
    DS1302_RST_HIGH();
    DS1302_Delay_us(2);
    
    DS1302_WriteByte(reg);
    DS1302_WriteByte(data);
    
    DS1302_RST_LOW();
    DS1302_Delay_us(2);
    
    return DS1302_OK;
}

/**
 * @brief 读取单个寄存器
 * @param reg 寄存器地址
 * @retval 读取的数据
 */
uint8_t DS1302_ReadReg(uint8_t reg)
{
    uint8_t data;
    
    DS1302_RST_LOW();
    DS1302_CLK_LOW();
    DS1302_Delay_us(2);
    
    DS1302_RST_HIGH();
    DS1302_Delay_us(2);
    
    DS1302_WriteByte(reg | 0x01);  // 读操作
    data = DS1302_ReadByte();
    
    DS1302_RST_LOW();
    DS1302_Delay_us(2);
    
    return data;
}

/**
 * @brief DS1302初始化
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_Init(void)
{
    // 初始化引脚状态
    DS1302_RST_LOW();
    DS1302_CLK_LOW();
    DS1302_DAT_LOW();
    
    DS1302_Delay_us(10);
    
    // 关闭写保护
    DS1302_WriteReg(DS1302_REG_WP, 0x00);
    
    // 禁用涓流充电（CR2032不可充电电池）
    DS1302_WriteReg(DS1302_REG_CHARGE, 0x00);
    
    // 确保时钟运行（清除CH位）
    uint8_t second_reg = DS1302_ReadReg(DS1302_REG_SECOND);
    if(second_reg & 0x80) {
        // 时钟停止，重新启动
        DS1302_WriteReg(DS1302_REG_SECOND, second_reg & 0x7F);
    }
    
    // 开启写保护
    DS1302_WriteReg(DS1302_REG_WP, 0x80);

    // 简化检测 - 直接尝试读取秒寄存器
    uint8_t test_read = DS1302_ReadReg(DS1302_REG_SECOND);
    if(test_read == 0xFF) {
        // 读取全1可能表示模块不存在
        return DS1302_ERROR;
    }
    
    return DS1302_OK;
}

/**
 * @brief 检查DS1302是否存在
 * @retval 1-存在，0-不存在
 */
uint8_t DS1302_IsPresent(void)
{
    uint8_t test_data = 0xAA;
    uint8_t read_data;
    
    // 写入测试数据到RAM
    DS1302_WriteReg(0xC0, test_data);  // RAM地址0
    read_data = DS1302_ReadReg(0xC1);  // 读取RAM地址0
    
    if(read_data == test_data)
    {
        return 1;  // DS1302存在
    }
    else
    {
        // 再测试另一个值
        test_data = 0x55;
        DS1302_WriteReg(0xC0, test_data);
        read_data = DS1302_ReadReg(0xC1);
        
        if(read_data == test_data)
        {
            return 1;  // DS1302存在
        }
    }
    
    return 0;  // DS1302不存在
}

/**
 * @brief 设置时间
 * @param time 时间结构体指针
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_SetTime(DS1302_Time_t *time)
{
    if(time == NULL)
    {
        return DS1302_ERROR;
    }

    // 参数范围检查
    if(time->year < 2000 || time->year > 2099 ||
       time->month < 1 || time->month > 12 ||
       time->date < 1 || time->date > 31 ||
       time->day < 1 || time->day > 7 ||
       time->hour > 23 || time->minute > 59 || time->second > 59)
    {
        return DS1302_ERROR;
    }

    // 关闭写保护
    DS1302_WriteReg(DS1302_REG_WP, 0x00);

    // 停止时钟
    DS1302_WriteReg(DS1302_REG_SECOND, 0x80);

    // 设置时间寄存器
    DS1302_WriteReg(DS1302_REG_YEAR, DS1302_DEC2BCD(time->year - 2000));
    DS1302_WriteReg(DS1302_REG_MONTH, DS1302_DEC2BCD(time->month));
    DS1302_WriteReg(DS1302_REG_DATE, DS1302_DEC2BCD(time->date));
    DS1302_WriteReg(DS1302_REG_DAY, DS1302_DEC2BCD(time->day));
    DS1302_WriteReg(DS1302_REG_HOUR, DS1302_DEC2BCD(time->hour));
    DS1302_WriteReg(DS1302_REG_MINUTE, DS1302_DEC2BCD(time->minute));

    // 启动时钟（设置秒并清除CH位）
    DS1302_WriteReg(DS1302_REG_SECOND, DS1302_DEC2BCD(time->second));

    // 开启写保护
    DS1302_WriteReg(DS1302_REG_WP, 0x80);

    return DS1302_OK;
}

/**
 * @brief 获取时间
 * @param time 时间结构体指针
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_GetTime(DS1302_Time_t *time)
{
    uint8_t temp;

    if(time == NULL)
    {
        return DS1302_ERROR;
    }

    // 读取时间寄存器
    temp = DS1302_ReadReg(DS1302_REG_SECOND);
    time->second = DS1302_BCD2DEC(temp & 0x7F);  // 清除CH位

    temp = DS1302_ReadReg(DS1302_REG_MINUTE);
    time->minute = DS1302_BCD2DEC(temp);

    temp = DS1302_ReadReg(DS1302_REG_HOUR);
    time->hour = DS1302_BCD2DEC(temp);

    temp = DS1302_ReadReg(DS1302_REG_DATE);
    time->date = DS1302_BCD2DEC(temp);

    temp = DS1302_ReadReg(DS1302_REG_MONTH);
    time->month = DS1302_BCD2DEC(temp);

    temp = DS1302_ReadReg(DS1302_REG_DAY);
    time->day = DS1302_BCD2DEC(temp);

    temp = DS1302_ReadReg(DS1302_REG_YEAR);
    time->year = DS1302_BCD2DEC(temp) + 2000;

    return DS1302_OK;
}

/**
 * @brief 检查时钟是否运行
 * @retval uint8_t 1-运行，0-停止
 */
uint8_t DS1302_IsClockRunning(void)
{
    uint8_t second_reg = DS1302_ReadReg(DS1302_REG_SECOND);
    return !(second_reg & 0x80);  // CH位为0表示时钟运行
}
