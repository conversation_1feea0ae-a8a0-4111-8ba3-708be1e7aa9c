/**
 ******************************************************************************
 * @file    AHT10.c
 * @brief   AHT10温湿度传感器驱动程序
 * <AUTHOR> Watch Project
 * @version V1.0
 * @date    2024
 ******************************************************************************
 */

#include "AHT10.h"
#include "i2c.h"
#include <string.h>

/* 全局变量 */
uint8_t aht10_calibrated = 0;

/**
 * @brief AHT10初始化
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_Init(void)
{
    HAL_StatusTypeDef hal_status;
    uint8_t init_cmd[3] = {0xE1, 0x08, 0x00}; // 根据参考代码

    // 等待传感器上电稳定（参考代码延时40ms）
    HAL_Delay(40);

    // 发送初始化命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, AHT10_ADDRESS_7BIT << 1, init_cmd, 3, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }

    // 等待初始化完成（参考代码延时40ms）
    HAL_Delay(40);

    aht10_calibrated = 1;
    return AHT10_OK;
}

/**
 * @brief AHT10软复位
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_SoftReset(void)
{
    HAL_StatusTypeDef hal_status;
    uint8_t reset_cmd = AHT10_CMD_SOFT_RESET;
    
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, AHT10_ADDRESS_7BIT << 1, &reset_cmd, 1, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }
    
    // 等待复位完成
    HAL_Delay(20);
    aht10_calibrated = 0;
    
    return AHT10_OK;
}

/**
 * @brief 读取AHT10状态
 * @param status 状态值指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadStatus(uint8_t *status)
{
    HAL_StatusTypeDef hal_status;
    
    if (status == NULL) {
        return AHT10_ERROR;
    }
    
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (AHT10_ADDRESS_7BIT << 1) | 1, status, 1, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }
    
    return AHT10_OK;
}

/**
 * @brief 检查AHT10是否忙碌
 * @retval uint8_t 1-忙碌，0-空闲
 */
uint8_t AHT10_IsBusy(void)
{
    uint8_t status;
    
    if (AHT10_ReadStatus(&status) != AHT10_OK) {
        return 1; // 读取失败时认为忙碌
    }
    
    return (status & AHT10_STATUS_BUSY) ? 1 : 0;
}

/**
 * @brief 检查AHT10是否已校准
 * @retval uint8_t 1-已校准，0-未校准
 */
uint8_t AHT10_IsCalibrated(void)
{
    return aht10_calibrated;
}

/**
 * @brief 触发AHT10测量
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_TriggerMeasurement(void)
{
    HAL_StatusTypeDef hal_status;
    uint8_t trigger_cmd[3] = {0xAC, 0x33, 0x00}; // 根据参考代码修正

    if (!aht10_calibrated) {
        return AHT10_NOT_CALIBRATED;
    }

    hal_status = HAL_I2C_Master_Transmit(&hi2c1, AHT10_ADDRESS_7BIT << 1, trigger_cmd, 3, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }

    return AHT10_OK;
}

/**
 * @brief 读取AHT10原始数据
 * @param data 数据缓冲区（6字节）
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadRawData(uint8_t *data)
{
    HAL_StatusTypeDef hal_status;

    if (data == NULL) {
        return AHT10_ERROR;
    }

    // 读取6字节数据（状态字节 + 5字节数据）
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (AHT10_ADDRESS_7BIT << 1) | 1, data, 6, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }

    return AHT10_OK;
}

/**
 * @brief 读取AHT10温湿度数据
 * @param aht_data 温湿度数据结构指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadData(AHT10_Data_t *aht_data)
{
    uint8_t raw_data[6];
    uint32_t humidity_raw, temperature_raw;

    if (aht_data == NULL) {
        return AHT10_ERROR;
    }

    // 读取原始数据
    if (AHT10_ReadRawData(raw_data) != AHT10_OK) {
        return AHT10_ERROR;
    }

    // 检查状态位（第一个字节的bit6应该为0表示测量完成）
    if (raw_data[0] & 0x40) {
        return AHT10_ERROR; // 传感器仍在测量中
    }

    // 根据参考代码提取湿度数据（20位）
    humidity_raw = ((uint32_t)raw_data[1] << 12) | ((uint32_t)raw_data[2] << 4) | ((uint32_t)raw_data[3] >> 4);

    // 根据参考代码提取温度数据（20位）
    temperature_raw = (((uint32_t)raw_data[3] & 0x0F) << 16) | ((uint32_t)raw_data[4] << 8) | (uint32_t)raw_data[5];

    // 根据参考代码计算实际值
    aht_data->humidity = (float)(humidity_raw * 100.0 / 1024 / 1024);
    aht_data->temperature = (float)(temperature_raw * 200.0 / 1024 / 1024) - 50.0f;

    return AHT10_OK;
}

/**
 * @brief 获取温湿度数据（一次完整的测量过程）
 * @param aht_data 温湿度数据结构指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_GetTemperatureHumidity(AHT10_Data_t *aht_data)
{
    HAL_StatusTypeDef hal_status;
    uint8_t trigger_cmd[3] = {0xAC, 0x33, 0x00};
    uint8_t raw_data[6];
    uint32_t SRH = 0, ST = 0;
    uint8_t ack;

    if (aht_data == NULL) {
        return AHT10_ERROR;
    }

    // 发送触发测量命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, AHT10_ADDRESS_7BIT << 1, trigger_cmd, 3, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }

    // 等待测量完成（根据参考代码）
    HAL_Delay(80);

    // 读取数据
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (AHT10_ADDRESS_7BIT << 1) | 1, raw_data, 6, AHT10_TIMEOUT);
    if (hal_status != HAL_OK) {
        return AHT10_ERROR;
    }

    // 检查状态位（根据参考代码：bit6应该为0）
    ack = raw_data[0];
    if ((ack & 0x40) == 0) {
        // 数据有效，按照参考代码计算
        SRH = (raw_data[1] << 12) + (raw_data[2] << 4) + (raw_data[3] >> 4);
        ST = ((raw_data[3] & 0x0F) << 16) + (raw_data[4] << 8) + raw_data[5];

        // 修正的公式计算（保留小数位）
        aht_data->humidity = (float)(SRH * 100.0 / 1024 / 1024);
        aht_data->temperature = (float)(ST * 200.0 / 1024 / 1024) - 50.0f;

        return AHT10_OK;
    }

    return AHT10_ERROR;
}

/**
 * @brief CRC8校验（如果需要的话）
 * @param data 数据指针
 * @param length 数据长度
 * @retval uint8_t CRC8值
 */
uint8_t AHT10_CRC8(uint8_t *data, uint8_t length)
{
    uint8_t crc = 0xFF;
    uint8_t i, j;

    for (i = 0; i < length; i++) {
        crc ^= data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x31;
            } else {
                crc <<= 1;
            }
        }
    }

    return crc;
}

/**
 * @brief 检测AHT10是否存在
 * @retval uint8_t 1-存在，0-不存在
 */
uint8_t AHT10_IsPresent(void)
{
    HAL_StatusTypeDef hal_status;

    // 尝试地址0x38 (7位)
    hal_status = HAL_I2C_IsDeviceReady(&hi2c1, 0x38 << 1, 3, 100);
    if (hal_status == HAL_OK) {
        return 1;
    }

    // 尝试地址0x39 (7位) - 有些AHT10可能使用这个地址
    hal_status = HAL_I2C_IsDeviceReady(&hi2c1, 0x39 << 1, 3, 100);
    if (hal_status == HAL_OK) {
        return 2; // 返回2表示使用0x39地址
    }

    return 0; // 都没找到
}

/**
 * @brief I2C总线扫描
 * @param found_devices 找到的设备地址数组
 * @param max_devices 最大设备数量
 * @retval uint8_t 找到的设备数量
 */
uint8_t AHT10_I2C_Scan(uint8_t *found_devices, uint8_t max_devices)
{
    HAL_StatusTypeDef hal_status;
    uint8_t device_count = 0;
    uint8_t address;

    if (found_devices == NULL || max_devices == 0) {
        return 0;
    }

    // 扫描I2C地址范围 0x08 到 0x77 (7位地址)
    for (address = 0x08; address <= 0x77 && device_count < max_devices; address++) {
        hal_status = HAL_I2C_IsDeviceReady(&hi2c1, address << 1, 1, 10);
        if (hal_status == HAL_OK) {
            found_devices[device_count] = address;
            device_count++;
        }
    }

    return device_count;
}

/**
 * @brief 简单测试函数
 * @retval uint8_t 测试结果
 */
uint8_t AHT10_SimpleTest(void)
{
    HAL_StatusTypeDef hal_status;
    uint8_t test_data;

    // 测试1: 检查设备是否响应
    hal_status = HAL_I2C_IsDeviceReady(&hi2c1, AHT10_ADDRESS_7BIT << 1, 3, 100);
    if (hal_status != HAL_OK) {
        return 1; // 设备不响应
    }

    // 测试2: 尝试读取状态
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (AHT10_ADDRESS_7BIT << 1) | 1, &test_data, 1, 100);
    if (hal_status != HAL_OK) {
        return 2; // 无法读取状态
    }

    return 0; // 测试通过
}
