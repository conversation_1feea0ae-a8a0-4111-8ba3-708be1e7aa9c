/**
  ******************************************************************************
  * @file    wifi_manager.c
  * @brief   WiFi管理模块实现 - ESP8266 AT指令通信
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "wifi_manager.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// WiFi模块状态
static wifi_status_t g_wifi_status = WIFI_STATUS_DISCONNECTED;
static wifi_connection_t g_wifi_connection = {0};

// 通信缓冲区
static char g_wifi_rx_buffer[WIFI_RX_BUFFER_SIZE];
static char g_wifi_tx_buffer[WIFI_TX_BUFFER_SIZE];
static volatile uint16_t g_rx_index = 0;
static volatile uint8_t g_rx_complete = 0;

// 互斥锁和信号量
static osMutexId_t g_wifi_mutex = NULL;
static osSemaphoreId_t g_response_sem = NULL;

/* Private function prototypes -----------------------------------------------*/
static void WiFi_ClearRxBuffer(void);
static int WiFi_WaitForResponse(const char* expected, uint32_t timeout);
static void WiFi_ParseResponse(const char* response);
static int WiFi_SendRawData(const char* data, uint16_t length);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief WiFi管理器初始化
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Init(void)
{
    // 创建互斥锁
    g_wifi_mutex = osMutexNew(NULL);
    if (g_wifi_mutex == NULL) {
        WIFI_LOG("Failed to create WiFi mutex");
        return -1;
    }
    
    // 创建响应信号量
    g_response_sem = osSemaphoreNew(1, 0, NULL);
    if (g_response_sem == NULL) {
        WIFI_LOG("Failed to create response semaphore");
        return -1;
    }
    
    // 初始化缓冲区
    WiFi_ClearRxBuffer();
    memset(&g_wifi_connection, 0, sizeof(g_wifi_connection));
    g_wifi_status = WIFI_STATUS_DISCONNECTED;
    
    // 启用UART接收中断
    HAL_UART_Receive_IT(&huart1, (uint8_t*)&g_wifi_rx_buffer[g_rx_index], 1);
    
    WIFI_LOG("WiFi Manager initialized");
    return 0;
}

/**
 * @brief 发送AT指令
 * @param command: AT指令字符串
 * @param timeout: 超时时间(ms)
 * @retval AT响应结果
 */
at_response_t WiFi_SendATCommand(const char* command, uint32_t timeout)
{
    if (command == NULL) {
        return AT_RESPONSE_INVALID;
    }
    
    // 获取互斥锁
    if (osMutexAcquire(g_wifi_mutex, timeout) != osOK) {
        return AT_RESPONSE_TIMEOUT;
    }
    
    // 清空接收缓冲区
    WiFi_ClearRxBuffer();
    
    // 发送AT指令
    snprintf(g_wifi_tx_buffer, WIFI_TX_BUFFER_SIZE, "%s\r\n", command);
    HAL_UART_Transmit(&huart1, (uint8_t*)g_wifi_tx_buffer, strlen(g_wifi_tx_buffer), 1000);
    
    WIFI_LOG("Sent: %s", command);
    
    // 等待响应
    at_response_t result = AT_RESPONSE_TIMEOUT;
    if (WiFi_WaitForResponse("OK", timeout) == 0) {
        result = AT_RESPONSE_OK;
    } else if (strstr(g_wifi_rx_buffer, "ERROR") != NULL) {
        result = AT_RESPONSE_ERROR;
    } else if (strstr(g_wifi_rx_buffer, "busy") != NULL) {
        result = AT_RESPONSE_BUSY;
    }
    
    // 释放互斥锁
    osMutexRelease(g_wifi_mutex);
    
    return result;
}

/**
 * @brief 发送AT指令并获取响应
 * @param command: AT指令字符串
 * @param response: 响应缓冲区
 * @param response_size: 响应缓冲区大小
 * @param timeout: 超时时间(ms)
 * @retval AT响应结果
 */
at_response_t WiFi_SendATCommandWithResponse(const char* command, char* response, 
                                           size_t response_size, uint32_t timeout)
{
    at_response_t result = WiFi_SendATCommand(command, timeout);
    
    if (response != NULL && response_size > 0) {
        strncpy(response, g_wifi_rx_buffer, response_size - 1);
        response[response_size - 1] = '\0';
    }
    
    return result;
}

/**
 * @brief 重置ESP8266模块
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Reset(void)
{
    WIFI_LOG("Resetting ESP8266...");
    
    at_response_t result = WiFi_SendATCommand("AT+RST", AT_TIMEOUT_LONG);
    if (result == AT_RESPONSE_OK) {
        // 等待模块重启完成
        osDelay(2000);
        g_wifi_status = WIFI_STATUS_DISCONNECTED;
        return 0;
    }
    
    return -1;
}

/**
 * @brief 检查ESP8266模块是否响应
 * @retval 0: 响应正常, -1: 无响应
 */
int WiFi_CheckModule(void)
{
    at_response_t result = WiFi_SendATCommand("AT", AT_TIMEOUT_SHORT);
    return (result == AT_RESPONSE_OK) ? 0 : -1;
}

/**
 * @brief 诊断串口通信问题
 * @retval 诊断结果字符串
 */
const char* WiFi_DiagnoseUART(void)
{
    static char diag_result[128];

    // 测试1: 基本AT指令
    WIFI_LOG("Testing basic AT command...");
    at_response_t result1 = WiFi_SendATCommand("AT", 1000);

    // 测试2: 获取版本信息
    WIFI_LOG("Testing version command...");
    at_response_t result2 = WiFi_SendATCommand("AT+GMR", 2000);

    // 测试3: 回显测试
    WIFI_LOG("Testing echo...");
    at_response_t result3 = WiFi_SendATCommand("ATE1", 1000);

    // 生成诊断报告
    snprintf(diag_result, sizeof(diag_result),
             "AT:%s GMR:%s ECHO:%s RX:%d",
             (result1 == AT_RESPONSE_OK) ? "OK" : "FAIL",
             (result2 == AT_RESPONSE_OK) ? "OK" : "FAIL",
             (result3 == AT_RESPONSE_OK) ? "OK" : "FAIL",
             g_rx_index);

    return diag_result;
}

/**
 * @brief 设置WiFi工作模式
 * @param mode: 1=Station, 2=AP, 3=Station+AP
 * @retval 0: 成功, -1: 失败
 */
int WiFi_SetMode(uint8_t mode)
{
    char command[32];
    snprintf(command, sizeof(command), "AT+CWMODE=%d", mode);
    
    at_response_t result = WiFi_SendATCommand(command, AT_TIMEOUT_MEDIUM);
    return (result == AT_RESPONSE_OK) ? 0 : -1;
}

/**
 * @brief 连接WiFi网络
 * @param ssid: 网络名称
 * @param password: 网络密码
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Connect(const char* ssid, const char* password)
{
    if (ssid == NULL || password == NULL) {
        return -1;
    }
    
    char command[128];
    snprintf(command, sizeof(command), "AT+CWJAP=\"%s\",\"%s\"", ssid, password);
    
    WIFI_LOG("Connecting to WiFi: %s", ssid);
    g_wifi_status = WIFI_STATUS_CONNECTING;
    
    at_response_t result = WiFi_SendATCommand(command, WIFI_CONNECT_TIMEOUT);
    if (result == AT_RESPONSE_OK) {
        g_wifi_status = WIFI_STATUS_CONNECTED;
        g_wifi_connection.status = WIFI_STATUS_CONNECTED;
        g_wifi_connection.connect_time = HAL_GetTick();
        
        // 获取IP地址
        WiFi_GetLocalIP(g_wifi_connection.local_ip, sizeof(g_wifi_connection.local_ip));
        
        WIFI_LOG("WiFi connected successfully");
        return 0;
    } else {
        g_wifi_status = WIFI_STATUS_ERROR;
        WIFI_LOG("WiFi connection failed");
        return -1;
    }
}

/**
 * @brief 断开WiFi连接
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Disconnect(void)
{
    at_response_t result = WiFi_SendATCommand("AT+CWQAP", AT_TIMEOUT_MEDIUM);
    if (result == AT_RESPONSE_OK) {
        g_wifi_status = WIFI_STATUS_DISCONNECTED;
        g_wifi_connection.status = WIFI_STATUS_DISCONNECTED;
        memset(g_wifi_connection.local_ip, 0, sizeof(g_wifi_connection.local_ip));
        
        WIFI_LOG("WiFi disconnected");
        return 0;
    }
    
    return -1;
}

/**
 * @brief 获取本地IP地址
 * @param ip_str: IP地址字符串缓冲区
 * @param size: 缓冲区大小
 * @retval 0: 成功, -1: 失败
 */
int WiFi_GetLocalIP(char* ip_str, size_t size)
{
    if (ip_str == NULL || size == 0) {
        return -1;
    }
    
    char response[256];
    at_response_t result = WiFi_SendATCommandWithResponse("AT+CIFSR", response, sizeof(response), AT_TIMEOUT_MEDIUM);
    
    if (result == AT_RESPONSE_OK) {
        // 解析IP地址 - 查找 +CIFSR:STAIP,"xxx.xxx.xxx.xxx"
        char* ip_start = strstr(response, "STAIP,\"");
        if (ip_start != NULL) {
            ip_start += 7; // 跳过 "STAIP,""
            char* ip_end = strchr(ip_start, '"');
            if (ip_end != NULL) {
                size_t ip_len = ip_end - ip_start;
                if (ip_len < size) {
                    strncpy(ip_str, ip_start, ip_len);
                    ip_str[ip_len] = '\0';
                    return 0;
                }
            }
        }
    }
    
    return -1;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 清空接收缓冲区
 */
static void WiFi_ClearRxBuffer(void)
{
    memset(g_wifi_rx_buffer, 0, WIFI_RX_BUFFER_SIZE);
    g_rx_index = 0;
    g_rx_complete = 0;
}

/**
 * @brief 等待指定响应
 * @param expected: 期望的响应字符串
 * @param timeout: 超时时间(ms)
 * @retval 0: 成功, -1: 超时
 */
static int WiFi_WaitForResponse(const char* expected, uint32_t timeout)
{
    uint32_t start_time = HAL_GetTick();
    
    while ((HAL_GetTick() - start_time) < timeout) {
        if (strstr(g_wifi_rx_buffer, expected) != NULL) {
            return 0;
        }
        osDelay(10);
    }
    
    return -1;
}

/**
 * @brief UART接收完成回调函数
 * @param huart: UART句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        // 检查缓冲区是否溢出
        if (g_rx_index < (WIFI_RX_BUFFER_SIZE - 1)) {
            g_rx_index++;
        } else {
            // 缓冲区溢出，重置
            WiFi_ClearRxBuffer();
        }
        
        // 继续接收下一个字符
        HAL_UART_Receive_IT(&huart1, (uint8_t*)&g_wifi_rx_buffer[g_rx_index], 1);
    }
}
