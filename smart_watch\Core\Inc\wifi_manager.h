/**
  ******************************************************************************
  * @file    wifi_manager.h
  * @brief   WiFi管理模块头文件 - ESP8266 AT指令通信
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

#ifndef __WIFI_MANAGER_H
#define __WIFI_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "cmsis_os.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief WiFi状态枚举
 */
typedef enum {
    WIFI_STATUS_DISCONNECTED = 0,   // 未连接
    WIFI_STATUS_CONNECTING,         // 连接中
    WIFI_STATUS_CONNECTED,          // 已连接
    WIFI_STATUS_ERROR,              // 错误状态
    WIFI_STATUS_TIMEOUT             // 超时
} wifi_status_t;

/**
 * @brief AT指令响应枚举
 */
typedef enum {
    AT_RESPONSE_OK = 0,             // 成功
    AT_RESPONSE_ERROR,              // 错误
    AT_RESPONSE_TIMEOUT,            // 超时
    AT_RESPONSE_BUSY,               // 忙碌
    AT_RESPONSE_INVALID             // 无效响应
} at_response_t;

/**
 * @brief WiFi网络信息结构体
 */
typedef struct {
    char ssid[32];                  // 网络名称
    char password[64];              // 网络密码
    int8_t rssi;                    // 信号强度
    uint8_t channel;                // 信道
    uint8_t encryption;             // 加密方式
} wifi_network_t;

/**
 * @brief WiFi连接信息结构体
 */
typedef struct {
    wifi_status_t status;           // 连接状态
    char local_ip[16];              // 本地IP地址
    char gateway[16];               // 网关地址
    char netmask[16];               // 子网掩码
    char mac_address[18];           // MAC地址
    uint32_t connect_time;          // 连接时间戳
} wifi_connection_t;

/* Exported constants --------------------------------------------------------*/

// AT指令超时时间定义
#define AT_TIMEOUT_SHORT        1000    // 短超时 1秒
#define AT_TIMEOUT_MEDIUM       5000    // 中等超时 5秒
#define AT_TIMEOUT_LONG         15000   // 长超时 15秒

// 缓冲区大小定义 - 优化内存使用
#define WIFI_RX_BUFFER_SIZE     512     // 接收缓冲区大小 (减少到512)
#define WIFI_TX_BUFFER_SIZE     256     // 发送缓冲区大小 (减少到256)
#define AT_COMMAND_MAX_LEN      128     // AT指令最大长度 (减少到128)

// WiFi配置参数
#define WIFI_MAX_RETRY_COUNT    3       // 最大重试次数
#define WIFI_SCAN_TIMEOUT       10000   // 扫描超时时间
#define WIFI_CONNECT_TIMEOUT    15000   // 连接超时时间

/* Exported macro ------------------------------------------------------------*/

// 调试输出宏（可以根据需要开启/关闭）
#ifdef WIFI_DEBUG
#define WIFI_LOG(fmt, ...) printf("[WIFI] " fmt "\r\n", ##__VA_ARGS__)
#else
#define WIFI_LOG(fmt, ...)
#endif

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief WiFi管理器初始化
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Init(void);

/**
 * @brief 发送AT指令
 * @param command: AT指令字符串
 * @param timeout: 超时时间(ms)
 * @retval AT响应结果
 */
at_response_t WiFi_SendATCommand(const char* command, uint32_t timeout);

/**
 * @brief 发送AT指令并获取响应
 * @param command: AT指令字符串
 * @param response: 响应缓冲区
 * @param response_size: 响应缓冲区大小
 * @param timeout: 超时时间(ms)
 * @retval AT响应结果
 */
at_response_t WiFi_SendATCommandWithResponse(const char* command, char* response, 
                                           size_t response_size, uint32_t timeout);

/**
 * @brief 重置ESP8266模块
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Reset(void);

/**
 * @brief 检查ESP8266模块是否响应
 * @retval 0: 响应正常, -1: 无响应
 */
int WiFi_CheckModule(void);

/**
 * @brief 诊断串口通信问题
 * @retval 诊断结果字符串
 */
const char* WiFi_DiagnoseUART(void);

/**
 * @brief 设置WiFi工作模式
 * @param mode: 1=Station, 2=AP, 3=Station+AP
 * @retval 0: 成功, -1: 失败
 */
int WiFi_SetMode(uint8_t mode);

/**
 * @brief 扫描WiFi网络
 * @param networks: 网络信息数组
 * @param max_count: 最大扫描数量
 * @retval 实际扫描到的网络数量, -1: 失败
 */
int WiFi_ScanNetworks(wifi_network_t* networks, uint8_t max_count);

/**
 * @brief 连接WiFi网络
 * @param ssid: 网络名称
 * @param password: 网络密码
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Connect(const char* ssid, const char* password);

/**
 * @brief 断开WiFi连接
 * @retval 0: 成功, -1: 失败
 */
int WiFi_Disconnect(void);

/**
 * @brief 获取连接状态
 * @param connection: 连接信息结构体指针
 * @retval 0: 成功, -1: 失败
 */
int WiFi_GetConnectionInfo(wifi_connection_t* connection);

/**
 * @brief 获取本地IP地址
 * @param ip_str: IP地址字符串缓冲区
 * @param size: 缓冲区大小
 * @retval 0: 成功, -1: 失败
 */
int WiFi_GetLocalIP(char* ip_str, size_t size);

/**
 * @brief WiFi状态监控任务（供FreeRTOS调用）
 * @param argument: 任务参数
 */
void WiFi_MonitorTask(void *argument);

#ifdef __cplusplus
}
#endif

#endif /* __WIFI_MANAGER_H */
