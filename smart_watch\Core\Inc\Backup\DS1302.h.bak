#ifndef __DS1302_H
#define __DS1302_H

#include <stdint.h>
#include "stm32f1xx_hal.h"

#ifdef __cplusplus
extern "C" {
#endif

/* DS1302 引脚定义 - 使用CubeMX生成的宏 */
#include "main.h"  // 包含CubeMX生成的引脚定义

#define DS1302_CLK_PORT     DS1302_CLK_GPIO_Port
#define DS1302_CLK_PIN      DS1302_CLK_Pin
#define DS1302_DAT_PORT     DS1302_DAT_GPIO_Port
#define DS1302_DAT_PIN      DS1302_DAT_Pin
#define DS1302_RST_PORT     DS1302_RST_GPIO_Port
#define DS1302_RST_PIN      DS1302_RST_Pin

/* DS1302 寄存器地址定义 */
#define DS1302_REG_SECOND       0x80    // 秒
#define DS1302_REG_MINUTE       0x82    // 分
#define DS1302_REG_HOUR         0x84    // 时
#define DS1302_REG_DATE         0x86    // 日
#define DS1302_REG_MONTH        0x88    // 月
#define DS1302_REG_DAY          0x8A    // 星期
#define DS1302_REG_YEAR         0x8C    // 年
#define DS1302_REG_WP           0x8E    // 写保护
#define DS1302_REG_CHARGE       0x90    // 充电设置

/* DS1302 控制位定义 */
#define DS1302_READ_BIT         0x01    // 读操作位
#define DS1302_WRITE_BIT        0x00    // 写操作位

/* DS1302 时间结构体 */
typedef struct {
    uint16_t year;      // 年 (2000-2099)
    uint8_t month;      // 月 (1-12)
    uint8_t date;       // 日 (1-31)
    uint8_t day;        // 星期 (1-7, 1=星期一)
    uint8_t hour;       // 时 (0-23)
    uint8_t minute;     // 分 (0-59)
    uint8_t second;     // 秒 (0-59)
} DS1302_Time_t;

/* DS1302 状态枚举 */
typedef enum {
    DS1302_OK = 0,
    DS1302_ERROR,
    DS1302_TIMEOUT
} DS1302_Status_t;

/* 引脚控制宏定义 */
#define DS1302_CLK_HIGH()   HAL_GPIO_WritePin(DS1302_CLK_PORT, DS1302_CLK_PIN, GPIO_PIN_SET)
#define DS1302_CLK_LOW()    HAL_GPIO_WritePin(DS1302_CLK_PORT, DS1302_CLK_PIN, GPIO_PIN_RESET)
#define DS1302_RST_HIGH()   HAL_GPIO_WritePin(DS1302_RST_PORT, DS1302_RST_PIN, GPIO_PIN_SET)
#define DS1302_RST_LOW()    HAL_GPIO_WritePin(DS1302_RST_PORT, DS1302_RST_PIN, GPIO_PIN_RESET)
#define DS1302_DAT_HIGH()   HAL_GPIO_WritePin(DS1302_DAT_PORT, DS1302_DAT_PIN, GPIO_PIN_SET)
#define DS1302_DAT_LOW()    HAL_GPIO_WritePin(DS1302_DAT_PORT, DS1302_DAT_PIN, GPIO_PIN_RESET)
#define DS1302_DAT_READ()   HAL_GPIO_ReadPin(DS1302_DAT_PORT, DS1302_DAT_PIN)

/* 函数声明 */

/**
 * @brief DS1302初始化
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_Init(void);

/**
 * @brief 设置时间
 * @param time 时间结构体指针
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_SetTime(DS1302_Time_t *time);

/**
 * @brief 获取时间
 * @param time 时间结构体指针
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_GetTime(DS1302_Time_t *time);

/**
 * @brief 写入单个寄存器
 * @param reg 寄存器地址
 * @param data 数据
 * @retval DS1302_Status_t 状态码
 */
DS1302_Status_t DS1302_WriteReg(uint8_t reg, uint8_t data);

/**
 * @brief 读取单个寄存器
 * @param reg 寄存器地址
 * @retval uint8_t 读取的数据
 */
uint8_t DS1302_ReadReg(uint8_t reg);

/**
 * @brief 设置DAT引脚为输出模式
 */
void DS1302_DAT_Output(void);

/**
 * @brief 设置DAT引脚为输入模式
 */
void DS1302_DAT_Input(void);

/**
 * @brief BCD转十进制
 * @param bcd BCD码
 * @retval uint8_t 十进制数
 */
uint8_t DS1302_BCD2DEC(uint8_t bcd);

/**
 * @brief 十进制转BCD
 * @param dec 十进制数
 * @retval uint8_t BCD码
 */
uint8_t DS1302_DEC2BCD(uint8_t dec);

/**
 * @brief 检查DS1302是否存在
 * @retval uint8_t 1-存在，0-不存在
 */
uint8_t DS1302_IsPresent(void);

/**
 * @brief 检查时钟是否运行
 * @retval uint8_t 1-运行，0-停止
 */
uint8_t DS1302_IsClockRunning(void);

#ifdef __cplusplus
}
#endif

#endif /* __DS1302_H */
