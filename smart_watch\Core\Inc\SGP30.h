#ifndef __SGP30_H
#define __SGP30_H

#include <stdint.h>
#include "stm32f1xx_hal.h"

#ifdef __cplusplus
extern "C" {
#endif

/* SGP30 I2C地址 */
#define SGP30_ADDRESS_7BIT      0x58    // 7位地址

/* SGP30 命令定义 */
#define SGP30_CMD_INIT_AIR_QUALITY      0x2003  // 初始化空气质量测量
#define SGP30_CMD_MEASURE_AIR_QUALITY   0x2008  // 测量空气质量
#define SGP30_CMD_GET_BASELINE          0x2015  // 获取基线
#define SGP30_CMD_SET_BASELINE          0x201E  // 设置基线
#define SGP30_CMD_SET_HUMIDITY          0x2061  // 设置湿度补偿
#define SGP30_CMD_MEASURE_TEST          0x2032  // 自测试
#define SGP30_CMD_GET_FEATURE_SET       0x202F  // 获取特征集
#define SGP30_CMD_MEASURE_RAW_SIGNALS   0x2050  // 测量原始信号
#define SGP30_CMD_GET_SERIAL_ID         0x3682  // 获取序列号

/* SGP30 参数定义 */
#define SGP30_TIMEOUT           1000    // I2C超时时间(ms)
#define SGP30_INIT_DELAY        15000   // 初始化延时(ms)
#define SGP30_MEASURE_DELAY     12      // 测量延时(ms)

/* SGP30 数据长度 */
#define SGP30_AIR_QUALITY_DATA_LENGTH   6   // 空气质量数据长度
#define SGP30_BASELINE_DATA_LENGTH      6   // 基线数据长度
#define SGP30_SERIAL_ID_LENGTH          9   // 序列号长度

/* SGP30 状态枚举 */
typedef enum {
    SGP30_OK = 0,
    SGP30_ERROR,
    SGP30_TIMEOUT_ERROR,
    SGP30_CRC_ERROR,
    SGP30_NOT_INITIALIZED
} SGP30_Status_t;

/* SGP30 数据结构 */
typedef struct {
    uint16_t tvoc_ppb;      // TVOC浓度 (ppb)
    uint16_t eco2_ppm;      // eCO2浓度 (ppm)
    uint8_t data_valid;     // 数据有效标志
} SGP30_Data_t;

/* SGP30 基线结构 */
typedef struct {
    uint16_t tvoc_baseline;
    uint16_t eco2_baseline;
} SGP30_Baseline_t;

/* 全局变量声明 */
extern uint8_t sgp30_initialized;

/* 函数声明 */

/**
 * @brief SGP30初始化
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_Init(void);

/**
 * @brief 获取空气质量数据
 * @param sgp_data 空气质量数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_GetAirQuality(SGP30_Data_t *sgp_data);

/**
 * @brief 设置湿度补偿
 * @param humidity 湿度值 (0-100%)
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_SetHumidity(float humidity);

/**
 * @brief 获取基线值
 * @param baseline 基线数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_GetBaseline(SGP30_Baseline_t *baseline);

/**
 * @brief 设置基线值
 * @param baseline 基线数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_SetBaseline(SGP30_Baseline_t *baseline);

/**
 * @brief 检测SGP30是否存在
 * @retval uint8_t 1-存在，0-不存在
 */
uint8_t SGP30_IsPresent(void);

/**
 * @brief CRC8校验
 * @param data 数据指针
 * @param length 数据长度
 * @retval uint8_t CRC8值
 */
uint8_t SGP30_CRC8(uint8_t *data, uint8_t length);

#ifdef __cplusplus
}
#endif

#endif /* __SGP30_H */
