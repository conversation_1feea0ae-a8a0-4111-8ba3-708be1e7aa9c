# DS1302 实时时钟模块集成说明

## 📋 概述

本项目已成功集成DS1302实时时钟模块，为智能手表提供准确的时间功能。

## 🔌 硬件连接

### 引脚连接
```
DS1302模块 → STM32F103C8T6
VCC       → 3.3V
GND       → GND
CLK       → PA5 (DS1302_CLK_Pin)
DAT       → PA6 (DS1302_DAT_Pin)
RST       → PA7 (DS1302_RST_Pin)
```

### 注意事项
- 确保DS1302模块上的CR2032电池已安装
- 电池为时钟提供备用电源，断电时保持时间运行
- 使用3.3V供电，避免5V损坏STM32

## 💻 软件架构

### 文件结构
```
Core/
├── Inc/
│   └── DS1302.h          # DS1302驱动头文件
└── Src/
    ├── DS1302.c          # DS1302驱动实现
    ├── main.c            # 主程序（包含初始化）
    └── freertos.c        # FreeRTOS任务（包含时钟任务）
```

### 主要功能
1. **DS1302_Init()** - 初始化DS1302模块
2. **DS1302_SetTime()** - 设置时间
3. **DS1302_GetTime()** - 获取当前时间
4. **DS1302_IsPresent()** - 检测模块是否存在

## 🚀 使用方法

### 1. 初始化
```c
DS1302_Time_t init_time = {
    .year = 2025,
    .month = 7,
    .date = 27,
    .day = 7,      // 星期日
    .hour = 12,
    .minute = 0,
    .second = 0
};

if(DS1302_Init() == DS1302_OK) {
    DS1302_SetTime(&init_time);  // 设置初始时间
}
```

### 2. 读取时间
```c
DS1302_Time_t current_time;
if(DS1302_GetTime(&current_time) == DS1302_OK) {
    // 使用current_time中的时间数据
    printf("Time: %04d-%02d-%02d %02d:%02d:%02d\n", 
           current_time.year, current_time.month, current_time.date,
           current_time.hour, current_time.minute, current_time.second);
}
```

## 📱 显示功能

### 当前实现
- **交替显示模式**：每5秒在时间和传感器数据之间切换
- **时间格式**：YYYY-MM-DD HH:MM:SS
- **实时更新**：每秒刷新显示

### 显示内容
```
模式1 - 时间显示：
Smart Watch
2025-07-27
12:00:00

模式2 - 传感器显示：
T:25.1C H:60.5%
TVOC:123 ppb
CO2:456 ppm
```

## 🔧 配置选项

### 时间设置
在 `main.c` 中修改初始时间：
```c
DS1302_Time_t init_time = {
    .year = 2025,    // 年份 (2000-2099)
    .month = 7,      // 月份 (1-12)
    .date = 27,      // 日期 (1-31)
    .day = 7,        // 星期 (1-7, 1=星期一)
    .hour = 12,      // 小时 (0-23)
    .minute = 0,     // 分钟 (0-59)
    .second = 0      // 秒钟 (0-59)
};
```

### 显示模式切换
在 `freertos.c` 中修改切换间隔：
```c
// 每5秒切换显示模式
if (HAL_GetTick() - last_switch_time >= 5000) {
    // 修改5000为其他值来改变切换间隔
}
```

## 🐛 故障排除

### 常见问题

1. **时间不准确**
   - 检查32.768kHz晶振是否正常
   - 确认电池电压是否充足
   - DS1302精度约±1-2分钟/月

2. **时间不保存**
   - 检查CR2032电池是否安装
   - 确认电池电压 > 2.5V
   - 检查写保护是否正确关闭

3. **无法读取时间**
   - 检查引脚连接是否正确
   - 确认GPIO配置是否正确
   - 使用DS1302_IsPresent()检测模块

### 调试方法

1. **检查模块存在**
```c
if(DS1302_IsPresent()) {
    // 模块存在
} else {
    // 模块不存在或连接错误
}
```

2. **验证时间设置**
```c
DS1302_SetTime(&test_time);
DS1302_GetTime(&read_time);
// 比较test_time和read_time是否一致
```

## 📈 性能特点

- **精度**：±1-2分钟/月（无温度补偿）
- **功耗**：< 1μA（备用模式）
- **通信速度**：软件模拟SPI，约100kHz
- **响应时间**：< 1ms

## 🔄 升级建议

如需更高精度，可考虑升级到DS3231：
- **精度**：±1分钟/年
- **温度补偿**：自动温度补偿
- **接口**：I2C（更简单）
- **成本**：稍高

## 📝 注意事项

1. **首次使用**：取消注释`DS1302_SetTime(&init_time)`来设置初始时间
2. **后续使用**：注释掉时间设置代码，避免每次重启都重置时间
3. **电池更换**：更换电池后需要重新设置时间
4. **精度要求**：如需高精度应用，建议升级到DS3231

---

**开发完成时间**：2025-07-27  
**开发者**：Augment Agent  
**状态**：✅ 完成并测试
