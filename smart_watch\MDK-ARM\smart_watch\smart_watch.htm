<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [smart_watch\smart_watch.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image smart_watch\smart_watch.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5050106: Last Updated: Thu Jul 31 17:17:28 2025
<BR><P>
<H3>Maximum Stack Usage =        328 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
StartDisplayTask &rArr; Display_Data_Log &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[7d]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC1_2_IRQHandler</a><BR>
 <LI><a href="#[8]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">BusFault_Handler</a><BR>
 <LI><a href="#[6]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">HardFault_Handler</a><BR>
 <LI><a href="#[7]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">MemManage_Handler</a><BR>
 <LI><a href="#[5]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">NMI_Handler</a><BR>
 <LI><a href="#[e3]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e3]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[9]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC1_2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f1xx_it.o(i.BusFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[19]">DMA1_Channel1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel6_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel7_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f1xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from stm32f1xx_it.o(i.EXTI15_10_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f1xx_it.o(i.HardFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f1xx_it.o(i.MemManage_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f1xx_it.o(i.NMI_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[37]">RTC_Alarm_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[11]">RTC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3e]">StartDefaultTask</a> from freertos.o(i.StartDefaultTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[41]">StartDisplayTask</a> from freertos.o(i.StartDisplayTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[3f]">StartKeyTask</a> from freertos.o(i.StartKeyTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[40]">StartSensorTask</a> from freertos.o(i.StartSensorTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[42]">StartWiFiTask</a> from freertos.o(i.StartWiFiTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[d]">SysTick_Handler</a> from cmsis_os2.o(i.SysTick_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[39]">SystemInit</a> from system_stm32f1xx.o(i.SystemInit) referenced from startup_stm32f103xb.o(.text)
 <LI><a href="#[10]">TAMPER_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[27]">TIM1_UP_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from stm32f1xx_it.o(i.TIM4_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[3d]">UART_DMAAbortOnError</a> from stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[33]">USART1_IRQHandler</a> from stm32f1xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[38]">USBWakeUp_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[21]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[22]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f1xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f103xb.o(.text) referenced from startup_stm32f103xb.o(RESET)
 <LI><a href="#[46]">__main</a> from __main.o(!!!main) referenced from startup_stm32f103xb.o(.text)
 <LI><a href="#[3c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[3b]">_snputc</a> from _snputc.o(.text) referenced from __2snprintf.o(.text)
 <LI><a href="#[44]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[43]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[45]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[46]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[47]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[49]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[185]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[186]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[187]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[188]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[189]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[4a]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 320 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[6b]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[4c]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[4e]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[50]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[18a]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[59]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[18b]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[18c]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[52]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[18d]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[18e]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[18f]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[190]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[191]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[192]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[54]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[193]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[194]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[195]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[196]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[197]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[198]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[199]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[19a]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[19b]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[19c]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[19d]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[19e]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[19f]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[5e]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[1a0]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[1a1]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[1a2]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[1a3]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[1a4]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000003))

<P><STRONG><a name="[1a5]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B))

<P><STRONG><a name="[48]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[1a6]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[56]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[58]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[1a7]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[5a]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 216 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MX_FREERTOS_Init &rArr; osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1a8]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[7e]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[5d]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1a9]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[5f]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[168]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[15f]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f103xb.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f103xb.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[62]"></a>__2snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Setting
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Interface
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Time_Setting
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Stopwatch_Interface
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Multi_Alarm_Setting
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Setting
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[66]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[67]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[65]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[4d]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[69]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[123]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[aa]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Repeat_Pattern
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
</UL>

<P><STRONG><a name="[a8]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Setting
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Interface
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Time_Setting
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Stopwatch_Interface
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Clock_Interface
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Setting
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[b0]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Repeat_Pattern
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
</UL>

<P><STRONG><a name="[be]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_And_Exit_Setting
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enter_Setting_Mode
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
</UL>

<P><STRONG><a name="[6c]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1aa]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_And_Exit_Setting
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Clock_Interface
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1ab]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1ac]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1ad]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[1ae]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[70]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[6f]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[120]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_ClearRxBuffer
</UL>

<P><STRONG><a name="[1af]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1b0]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[171]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[1b1]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1b2]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1b3]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[68]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[1b4]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[63]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[64]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[3b]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __2snprintf.o(.text)
</UL>
<P><STRONG><a name="[7b]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[4f]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[51]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[1b5]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1b6]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[77]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7a]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[73]"></a>_btod_etento</STRONG> (Thumb, 216 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[57]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[5c]"></a>exit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[60]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[1b7]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1b8]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1b9]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[80]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[7f]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[81]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[82]"></a>_e2e</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[75]"></a>_btod_ediv</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[76]"></a>_btod_emul</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[83]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[84]"></a>AHT10_GetTemperatureHumidity</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, aht10.o(i.AHT10_GetTemperatureHumidity))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = AHT10_GetTemperatureHumidity &rArr; HAL_I2C_Master_Receive &rArr; I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[8d]"></a>AHT10_Init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, aht10.o(i.AHT10_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AHT10_Init &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[8e]"></a>AHT10_SimpleTest</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, aht10.o(i.AHT10_SimpleTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = AHT10_SimpleTest &rArr; HAL_I2C_Master_Receive &rArr; I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>Buzzer_Start_Alarm</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, freertos.o(i.Buzzer_Start_Alarm))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Buzzer_Start_Alarm
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Multi_Alarms
</UL>

<P><STRONG><a name="[92]"></a>Buzzer_Start_Timer</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, freertos.o(i.Buzzer_Start_Timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Buzzer_Start_Timer
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Timer
</UL>

<P><STRONG><a name="[93]"></a>Buzzer_Stop</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, freertos.o(i.Buzzer_Stop))
<BR><BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Update
</UL>

<P><STRONG><a name="[95]"></a>Buzzer_Update</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, freertos.o(i.Buzzer_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Buzzer_Update
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Stop
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[97]"></a>Check_Alarm_Repeat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, freertos.o(i.Check_Alarm_Repeat))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Multi_Alarms
</UL>

<P><STRONG><a name="[96]"></a>Check_Multi_Alarms</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, freertos.o(i.Check_Multi_Alarms))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Check_Multi_Alarms &rArr; Buzzer_Start_Alarm
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Alarm_Repeat
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Start_Alarm
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[9d]"></a>DS1302_BCD2DEC</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ds1302.o(i.DS1302_BCD2DEC))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_GetTime
</UL>

<P><STRONG><a name="[98]"></a>DS1302_DAT_Input</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, ds1302.o(i.DS1302_DAT_Input))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DS1302_DAT_Input &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
</UL>

<P><STRONG><a name="[9a]"></a>DS1302_DAT_Output</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, ds1302.o(i.DS1302_DAT_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteByte
</UL>

<P><STRONG><a name="[a4]"></a>DS1302_DEC2BCD</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ds1302.o(i.DS1302_DEC2BCD))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_SetTime
</UL>

<P><STRONG><a name="[9b]"></a>DS1302_GetTime</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, ds1302.o(i.DS1302_GetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DS1302_GetTime &rArr; DS1302_ReadReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_BCD2DEC
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9e]"></a>DS1302_Init</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, ds1302.o(i.DS1302_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DS1302_Init &rArr; DS1302_WriteReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteReg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>DS1302_ReadReg</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, ds1302.o(i.DS1302_ReadReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = DS1302_ReadReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_DAT_Input
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteByte
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_GetTime
</UL>

<P><STRONG><a name="[a3]"></a>DS1302_SetTime</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, ds1302.o(i.DS1302_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DS1302_SetTime &rArr; DS1302_WriteReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteReg
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_DEC2BCD
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_And_Exit_Setting
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>DS1302_WriteReg</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, ds1302.o(i.DS1302_WriteReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = DS1302_WriteReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteByte
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_SetTime
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Init
</UL>

<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>Decrease_Setting_Value</STRONG> (Thumb, 358 bytes, Stack size 16 bytes, freertos.o(i.Decrease_Setting_Value))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Decrease_Setting_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[a6]"></a>Display_Alarm_Setting</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, freertos.o(i.Display_Alarm_Setting))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = Display_Alarm_Setting &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[a9]"></a>Display_Alarm_Simple</STRONG> (Thumb, 168 bytes, Stack size 112 bytes, freertos.o(i.Display_Alarm_Simple))
<BR><BR>[Stack]<UL><LI>Max Depth = 256 + Unknown Stack Size
<LI>Call Chain = Display_Alarm_Simple &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Repeat_Pattern
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[ad]"></a>Display_Clock_Interface</STRONG> (Thumb, 420 bytes, Stack size 112 bytes, freertos.o(i.Display_Clock_Interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Display_Clock_Interface &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[ae]"></a>Display_Data_Log</STRONG> (Thumb, 522 bytes, Stack size 160 bytes, freertos.o(i.Display_Data_Log))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + Unknown Stack Size
<LI>Call Chain = Display_Data_Log &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b1]"></a>Display_Multi_Alarm_Setting</STRONG> (Thumb, 200 bytes, Stack size 112 bytes, freertos.o(i.Display_Multi_Alarm_Setting))
<BR><BR>[Stack]<UL><LI>Max Depth = 256 + Unknown Stack Size
<LI>Call Chain = Display_Multi_Alarm_Setting &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Repeat_Pattern
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[ac]"></a>Display_Repeat_Pattern</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, freertos.o(i.Display_Repeat_Pattern))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Display_Repeat_Pattern &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Multi_Alarm_Setting
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
</UL>

<P><STRONG><a name="[b2]"></a>Display_Sensor_Interface</STRONG> (Thumb, 554 bytes, Stack size 112 bytes, freertos.o(i.Display_Sensor_Interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Display_Sensor_Interface &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b5]"></a>Display_Stopwatch_Interface</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, freertos.o(i.Display_Stopwatch_Interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = Display_Stopwatch_Interface &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b6]"></a>Display_Time_Setting</STRONG> (Thumb, 212 bytes, Stack size 64 bytes, freertos.o(i.Display_Time_Setting))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = Display_Time_Setting &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b7]"></a>Display_Timer_Interface</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, freertos.o(i.Display_Timer_Interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = Display_Timer_Interface &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b8]"></a>Display_Timer_Setting</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, freertos.o(i.Display_Timer_Setting))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = Display_Timer_Setting &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[b9]"></a>Display_Weather_Interface</STRONG> (Thumb, 298 bytes, Stack size 104 bytes, freertos.o(i.Display_Weather_Interface))
<BR><BR>[Stack]<UL><LI>Max Depth = 248 + Unknown Stack Size
<LI>Call Chain = Display_Weather_Interface &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Weather_StatusToIcon
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f1xx_it.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = EXTI15_10_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[bd]"></a>Enter_Setting_Mode</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, freertos.o(i.Enter_Setting_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Enter_Setting_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[fc]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[e6]"></a>HAL_DMA_Abort</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e4]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 148 bytes, Stack size 8 bytes, stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[86]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f1xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_GetAirQuality
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PowerOnInit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[bf]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, freertos.o(i.HAL_GPIO_EXTI_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Stop
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[bc]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>HAL_GPIO_Init</STRONG> (Thumb, 446 bytes, Stack size 40 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_DAT_Output
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_DAT_Input
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[94]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteReg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteByte
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Update
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Stop
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[91]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Timer
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Record_Sensor_Data
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Next_Setting_Item
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Increase_Setting_Value
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enter_Setting_Mode
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Stopwatch_Interface
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Decrease_Setting_Value
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Update
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Start_Timer
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Start_Alarm
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[c0]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[8f]"></a>HAL_I2C_IsDeviceReady</STRONG> (Thumb, 354 bytes, Stack size 56 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_IsDeviceReady &rArr; I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_IsPresent
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_SimpleTest
</UL>

<P><STRONG><a name="[87]"></a>HAL_I2C_Master_Receive</STRONG> (Thumb, 576 bytes, Stack size 40 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_I2C_Master_Receive &rArr; I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestRead
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_GetAirQuality
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_SimpleTest
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[85]"></a>HAL_I2C_Master_Transmit</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_GetAirQuality
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[c1]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[e0]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f1xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[c9]"></a>HAL_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f1xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cb]"></a>HAL_InitTick</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[cc]"></a>HAL_MspInit</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f1xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[d0]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d1]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[ca]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[d3]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 280 bytes, Stack size 32 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[cd]"></a>HAL_RCC_GetClockConfig</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[c2]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[121]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[d4]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[d5]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 778 bytes, Stack size 40 bytes, stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[dd]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[df]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[ce]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d6]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[cf]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[d9]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[d8]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 304 bytes, Stack size 24 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[db]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[dc]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[de]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[e5]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[e1]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 616 bytes, Stack size 24 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; WiFi_ClearRxBuffer &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[ea]"></a>HAL_UART_MspInit</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[ec]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[ee]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, wifi_manager.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_UART_RxCpltCallback &rArr; WiFi_ClearRxBuffer &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_ClearRxBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[f0]"></a>HAL_UART_Transmit</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[e8]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[f4]"></a>Increase_Setting_Value</STRONG> (Thumb, 362 bytes, Stack size 16 bytes, freertos.o(i.Increase_Setting_Value))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Increase_Setting_Value
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[f5]"></a>MX_FREERTOS_Init</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, freertos.o(i.MX_FREERTOS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = MX_FREERTOS_Init &rArr; osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueNew
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexNew
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fa]"></a>MX_GPIO_Init</STRONG> (Thumb, 190 bytes, Stack size 48 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fb]"></a>MX_I2C1_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>MX_I2C2_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[ff]"></a>Next_Setting_Item</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, freertos.o(i.Next_Setting_Item))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Next_Setting_Item
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[ab]"></a>OLED_Clear</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PowerOnInit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Multi_Alarm_Setting
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
</UL>

<P><STRONG><a name="[107]"></a>OLED_ClearArea</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, oled.o(i.OLED_ClearArea))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>

<P><STRONG><a name="[100]"></a>OLED_Init</STRONG> (Thumb, 182 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_Init &rArr; OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PowerOnInit
</UL>

<P><STRONG><a name="[103]"></a>OLED_PowerOnInit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, oled.o(i.OLED_PowerOnInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_PowerOnInit &rArr; OLED_Init &rArr; OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[104]"></a>OLED_SetCursor</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = OLED_SetCursor &rArr; OLED_WriteCommand &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[105]"></a>OLED_ShowChar</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[106]"></a>OLED_ShowImage</STRONG> (Thumb, 182 bytes, Stack size 36 bytes, oled.o(i.OLED_ShowImage))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[a7]"></a>OLED_ShowString</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Setting
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Interface
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Time_Setting
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Stopwatch_Interface
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Multi_Alarm_Setting
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Clock_Interface
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Setting
</UL>

<P><STRONG><a name="[102]"></a>OLED_Update</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, oled.o(i.OLED_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PowerOnInit
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[101]"></a>OLED_WriteCommand</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, oled.o(i.OLED_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OLED_WriteCommand &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[108]"></a>OLED_WriteData</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, oled.o(i.OLED_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OLED_WriteData &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[109]"></a>Record_Sensor_Data</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, freertos.o(i.Record_Sensor_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Record_Sensor_Data &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Daily_Stats
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[10d]"></a>SGP30_CRC8</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, sgp30.o(i.SGP30_CRC8))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SGP30_CRC8
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_GetAirQuality
</UL>

<P><STRONG><a name="[10c]"></a>SGP30_GetAirQuality</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, sgp30.o(i.SGP30_GetAirQuality))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = SGP30_GetAirQuality &rArr; HAL_I2C_Master_Receive &rArr; I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_CRC8
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[10e]"></a>SGP30_Init</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, sgp30.o(i.SGP30_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SGP30_Init &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[10f]"></a>SGP30_IsPresent</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, sgp30.o(i.SGP30_IsPresent))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SGP30_IsPresent &rArr; HAL_I2C_IsDeviceReady &rArr; I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[110]"></a>SGP30_SetHumidity</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, sgp30.o(i.SGP30_SetHumidity))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SGP30_SetHumidity &rArr; HAL_I2C_Master_Transmit &rArr; I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_CRC8
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[113]"></a>Save_And_Exit_Setting</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, freertos.o(i.Save_And_Exit_Setting))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Save_And_Exit_Setting &rArr; DS1302_SetTime &rArr; DS1302_WriteReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_SetTime
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
</UL>

<P><STRONG><a name="[3e]"></a>StartDefaultTask</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, freertos.o(i.StartDefaultTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = StartDefaultTask &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[41]"></a>StartDisplayTask</STRONG> (Thumb, 610 bytes, Stack size 24 bytes, freertos.o(i.StartDisplayTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = StartDisplayTask &rArr; Display_Data_Log &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_GetTime
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_PowerOnInit
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueGet
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Timer
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Record_Sensor_Data
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Setting
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Timer_Interface
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Time_Setting
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Stopwatch_Interface
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Multi_Alarm_Setting
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Clock_Interface
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Simple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Alarm_Setting
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Multi_Alarms
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Update
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexRelease
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexAcquire
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[3f]"></a>StartKeyTask</STRONG> (Thumb, 506 bytes, Stack size 0 bytes, freertos.o(i.StartKeyTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = StartKeyTask &rArr; Save_And_Exit_Setting &rArr; DS1302_SetTime &rArr; DS1302_WriteReg &rArr; DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_And_Exit_Setting
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Next_Setting_Item
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Increase_Setting_Value
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enter_Setting_Mode
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Decrease_Setting_Value
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Stop
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[40]"></a>StartSensorTask</STRONG> (Thumb, 350 bytes, Stack size 16 bytes, freertos.o(i.StartSensorTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = StartSensorTask &rArr; SGP30_GetAirQuality &rArr; HAL_I2C_Master_Receive &rArr; I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_IsPresent
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_GetAirQuality
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_SimpleTest
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueuePut
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[42]"></a>StartWiFiTask</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, freertos.o(i.StartWiFiTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = StartWiFiTask &rArr; WiFi_CheckModule &rArr; WiFi_SendATCommand &rArr; osMutexAcquire &rArr; xQueueTakeMutexRecursive &rArr; xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_CheckModule
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, cmsis_os2.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[11f]"></a>SystemClock_Config</STRONG> (Thumb, 94 bytes, Stack size 72 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[39]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f1xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(.text)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM4_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[d7]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 108 bytes, Stack size 20 bytes, stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[ed]"></a>UART_Start_Receive_IT</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>

<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; WiFi_ClearRxBuffer &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[10b]"></a>Update_Daily_Stats</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, freertos.o(i.Update_Daily_Stats))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Record_Sensor_Data
</UL>

<P><STRONG><a name="[115]"></a>Update_Timer</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, freertos.o(i.Update_Timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Update_Timer &rArr; Buzzer_Start_Timer
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Buzzer_Start_Timer
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f1xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f103xb.o(RESET)
</UL>
<P><STRONG><a name="[ba]"></a>Weather_StatusToIcon</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, weather_api.o(i.Weather_StatusToIcon))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
</UL>

<P><STRONG><a name="[11c]"></a>WiFi_CheckModule</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, wifi_manager.o(i.WiFi_CheckModule))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = WiFi_CheckModule &rArr; WiFi_SendATCommand &rArr; osMutexAcquire &rArr; xQueueTakeMutexRecursive &rArr; xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
</UL>

<P><STRONG><a name="[11b]"></a>WiFi_Init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, wifi_manager.o(i.WiFi_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = WiFi_Init &rArr; osMutexNew &rArr; xQueueCreateMutexStatic &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexNew
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_ClearRxBuffer
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
</UL>

<P><STRONG><a name="[122]"></a>WiFi_SendATCommand</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, wifi_manager.o(i.WiFi_SendATCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = WiFi_SendATCommand &rArr; osMutexAcquire &rArr; xQueueTakeMutexRecursive &rArr; xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexRelease
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexAcquire
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_ClearRxBuffer
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_CheckModule
</UL>

<P><STRONG><a name="[79]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[6a]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[5b]"></a>main</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 216 + Unknown Stack Size
<LI>Call Chain = main &rArr; MX_FREERTOS_Init &rArr; osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_SetTime
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_GetTime
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelInitialize
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[114]"></a>osDelay</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, cmsis_os2.o(i.osDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartWiFiTask
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartKeyTask
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDefaultTask
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[124]"></a>osKernelInitialize</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, cmsis_os2.o(i.osKernelInitialize))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[125]"></a>osKernelStart</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, cmsis_os2.o(i.osKernelStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = osKernelStart &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask &rArr; pxPortInitialiseStack
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[117]"></a>osMessageQueueGet</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, cmsis_os2.o(i.osMessageQueueGet))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = osMessageQueueGet &rArr; xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[f8]"></a>osMessageQueueNew</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, cmsis_os2.o(i.osMessageQueueNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = osMessageQueueNew &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[11a]"></a>osMessageQueuePut</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, cmsis_os2.o(i.osMessageQueuePut))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = osMessageQueuePut &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
</UL>

<P><STRONG><a name="[116]"></a>osMutexAcquire</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, cmsis_os2.o(i.osMutexAcquire))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = osMutexAcquire &rArr; xQueueTakeMutexRecursive &rArr; xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueTakeMutexRecursive
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[f6]"></a>osMutexNew</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, cmsis_os2.o(i.osMutexNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = osMutexNew &rArr; xQueueCreateMutexStatic &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutexStatic
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
</UL>

<P><STRONG><a name="[119]"></a>osMutexRelease</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, cmsis_os2.o(i.osMutexRelease))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = osMutexRelease &rArr; xQueueGiveMutexRecursive &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveMutexRecursive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[f7]"></a>osSemaphoreNew</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, cmsis_os2.o(i.osSemaphoreNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = osSemaphoreNew &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphoreStatic
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphore
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
</UL>

<P><STRONG><a name="[f9]"></a>osThreadNew</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, cmsis_os2.o(i.osThreadNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[15b]"></a>pvPortMalloc</STRONG> (Thumb, 216 bytes, Stack size 24 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[16d]"></a>pvTaskIncrementMutexHeldCount</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, tasks.o(i.pvTaskIncrementMutexHeldCount))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[14c]"></a>pxPortInitialiseStack</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pxPortInitialiseStack
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[13a]"></a>uxListRemove</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
</UL>

<P><STRONG><a name="[164]"></a>vApplicationGetIdleTaskMemory</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetIdleTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[170]"></a>vApplicationGetTimerTaskMemory</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetTimerTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[13f]"></a>vListInitialise</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[14b]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[13c]"></a>vListInsert</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[13b]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
</UL>

<P><STRONG><a name="[13e]"></a>vPortEnterCritical</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
</UL>

<P><STRONG><a name="[140]"></a>vPortExitCritical</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
</UL>

<P><STRONG><a name="[146]"></a>vPortFree</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>

<P><STRONG><a name="[167]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[15e]"></a>vPortValidateInterruptPriority</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, port.o(i.vPortValidateInterruptPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortGetIPSR
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
</UL>

<P><STRONG><a name="[12c]"></a>vQueueAddToRegistry</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueNew
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexNew
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[135]"></a>vQueueDelete</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, queue.o(i.vQueueDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = vQueueDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueUnregisterQueue
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
</UL>

<P><STRONG><a name="[160]"></a>vQueueUnregisterQueue</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, queue.o(i.vQueueUnregisterQueue))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>

<P><STRONG><a name="[155]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[126]"></a>vTaskDelay</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>

<P><STRONG><a name="[16a]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
</UL>

<P><STRONG><a name="[15a]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[162]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[161]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[163]"></a>vTaskPriorityDisinheritAfterTimeout</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, tasks.o(i.vTaskPriorityDisinheritAfterTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskPriorityDisinheritAfterTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[127]"></a>vTaskStartScheduler</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask &rArr; pxPortInitialiseStack
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetIdleTaskMemory
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
</UL>

<P><STRONG><a name="[153]"></a>vTaskSuspendAll</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>

<P><STRONG><a name="[61]"></a>vTaskSwitchContext</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[166]"></a>xPortStartScheduler</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[11e]"></a>xPortSysTickHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[136]"></a>xQueueCreateCountingSemaphore</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, queue.o(i.xQueueCreateCountingSemaphore))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xQueueCreateCountingSemaphore &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
</UL>

<P><STRONG><a name="[134]"></a>xQueueCreateCountingSemaphoreStatic</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, queue.o(i.xQueueCreateCountingSemaphoreStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = xQueueCreateCountingSemaphoreStatic &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
</UL>

<P><STRONG><a name="[132]"></a>xQueueCreateMutex</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, queue.o(i.xQueueCreateMutex))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xQueueCreateMutex &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseMutex
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexNew
</UL>

<P><STRONG><a name="[131]"></a>xQueueCreateMutexStatic</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, queue.o(i.xQueueCreateMutexStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = xQueueCreateMutexStatic &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseMutex
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexNew
</UL>

<P><STRONG><a name="[12b]"></a>xQueueGenericCreate</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueNew
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphore
</UL>

<P><STRONG><a name="[12a]"></a>xQueueGenericCreateStatic</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueNew
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutexStatic
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphoreStatic
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[149]"></a>xQueueGenericReset</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[12e]"></a>xQueueGenericSend</STRONG> (Thumb, 346 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueuePut
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osSemaphoreNew
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexRelease
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveMutexRecursive
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseMutex
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[12d]"></a>xQueueGenericSendFromISR</STRONG> (Thumb, 190 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericSendFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xQueueGenericSendFromISR &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueuePut
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[133]"></a>xQueueGiveMutexRecursive</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, queue.o(i.xQueueGiveMutexRecursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xQueueGiveMutexRecursive &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexRelease
</UL>

<P><STRONG><a name="[129]"></a>xQueueReceive</STRONG> (Thumb, 308 bytes, Stack size 56 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueGet
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[128]"></a>xQueueReceiveFromISR</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, queue.o(i.xQueueReceiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = xQueueReceiveFromISR &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMessageQueueGet
</UL>

<P><STRONG><a name="[130]"></a>xQueueSemaphoreTake</STRONG> (Thumb, 372 bytes, Stack size 48 bytes, queue.o(i.xQueueSemaphoreTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvTaskIncrementMutexHeldCount
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexAcquire
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueTakeMutexRecursive
</UL>

<P><STRONG><a name="[12f]"></a>xQueueTakeMutexRecursive</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, queue.o(i.xQueueTakeMutexRecursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xQueueTakeMutexRecursive &rArr; xQueueSemaphoreTake &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osMutexAcquire
</UL>

<P><STRONG><a name="[16b]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[138]"></a>xTaskCreate</STRONG> (Thumb, 90 bytes, Stack size 56 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
</UL>

<P><STRONG><a name="[137]"></a>xTaskCreateStatic</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, tasks.o(i.xTaskCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = xTaskCreateStatic &rArr; prvInitialiseNewTask &rArr; pxPortInitialiseStack
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[16c]"></a>xTaskGetCurrentTaskHandle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetCurrentTaskHandle))
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueTakeMutexRecursive
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveMutexRecursive
</UL>

<P><STRONG><a name="[11d]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[156]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[169]"></a>xTaskIncrementTick</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[144]"></a>xTaskPriorityDisinherit</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityDisinherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>

<P><STRONG><a name="[16e]"></a>xTaskPriorityInherit</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityInherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityInherit
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[159]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[154]"></a>xTaskResumeAll</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>

<P><STRONG><a name="[165]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask &rArr; pxPortInitialiseStack
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetTimerTaskMemory
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[151]"></a>xTimerGenericCommand</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, timers.o(i.xTimerGenericCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[55]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[8b]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[172]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[175]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[1ba]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[89]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[177]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[174]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[178]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Weather_Interface
</UL>

<P><STRONG><a name="[179]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[b4]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
</UL>

<P><STRONG><a name="[17b]"></a>_fadd</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[17d]"></a>__fpl_fcheck_NaN1</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fcheck1.o(x$fpl$fcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbnf
</UL>

<P><STRONG><a name="[112]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[17e]"></a>_fdiv</STRONG> (Thumb, 384 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[af]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Record_Sensor_Data
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Data_Log
</UL>

<P><STRONG><a name="[17f]"></a>_ffix</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[10a]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Record_Sensor_Data
</UL>

<P><STRONG><a name="[180]"></a>_ffixu</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[118]"></a>__aeabi_ui2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDisplayTask
</UL>

<P><STRONG><a name="[1bb]"></a>_ffltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu), UNUSED)

<P><STRONG><a name="[b3]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartSensorTask
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Sensor_Interface
</UL>

<P><STRONG><a name="[181]"></a>_fmul</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[17a]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffixu
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffix
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[173]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[8c]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[182]"></a>_fsub</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[4b]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[176]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[8a]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHT10_GetTemperatureHumidity
</UL>

<P><STRONG><a name="[111]"></a>__ARM_scalbnf</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, scalbnf.o(x$fpl$scalbnf))
<BR><BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SGP30_SetHumidity
</UL>

<P><STRONG><a name="[184]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9f]"></a>DS1302_Delay_us</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ds1302.o(i.DS1302_Delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DS1302_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteReg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteByte
</UL>

<P><STRONG><a name="[a1]"></a>DS1302_WriteByte</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, ds1302.o(i.DS1302_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = DS1302_WriteByte &rArr; DS1302_DAT_Output &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_DAT_Output
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_WriteReg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DS1302_ReadReg
</UL>

<P><STRONG><a name="[ef]"></a>WiFi_ClearRxBuffer</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, wifi_manager.o(i.WiFi_ClearRxBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = WiFi_ClearRxBuffer &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WiFi_SendATCommand
</UL>

<P><STRONG><a name="[d2]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[f3]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[c4]"></a>I2C_MasterRequestRead</STRONG> (Thumb, 230 bytes, Stack size 40 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = I2C_MasterRequestRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
</UL>

<P><STRONG><a name="[c6]"></a>I2C_MasterRequestWrite</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_MasterRequestWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[c8]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[c3]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestRead
</UL>

<P><STRONG><a name="[f2]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestWrite
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterRequestRead
</UL>

<P><STRONG><a name="[c5]"></a>I2C_WaitOnRXNEFlagUntilTimeout</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnRXNEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Receive
</UL>

<P><STRONG><a name="[c7]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[3d]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[e3]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f1xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>

<P><STRONG><a name="[e2]"></a>UART_Receive_IT</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, stm32f1xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART_Receive_IT &rArr; HAL_UART_RxCpltCallback &rArr; WiFi_ClearRxBuffer &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[eb]"></a>UART_SetConfig</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, stm32f1xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[f1]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[142]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvCopyDataFromQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[143]"></a>prvCopyDataToQueue</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[147]"></a>prvInitialiseMutex</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, queue.o(i.prvInitialiseMutex))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = prvInitialiseMutex &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutexStatic
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
</UL>

<P><STRONG><a name="[148]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[14e]"></a>prvIsQueueEmpty</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvIsQueueEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[158]"></a>prvUnlockQueue</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[139]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[13d]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvAddNewTaskToReadyList
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[145]"></a>prvDeleteTCB</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[44]"></a>prvIdleTask</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = prvIdleTask &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[14a]"></a>prvInitialiseNewTask</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 48 + Unknown Stack Size
<LI>Call Chain = prvInitialiseNewTask &rArr; pxPortInitialiseStack
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[16f]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>

<P><STRONG><a name="[141]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[14d]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[14f]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 248 bytes, Stack size 48 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[152]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[150]"></a>prvSampleTimeNow</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[157]"></a>prvSwitchTimerLists</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[45]"></a>prvTimerTask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[15c]"></a>prvHeapInit</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[15d]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[43]"></a>prvTaskExitError</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[183]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[17c]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>

<P><STRONG><a name="[72]"></a>_fp_digits</STRONG> (Thumb, 428 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[3c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
