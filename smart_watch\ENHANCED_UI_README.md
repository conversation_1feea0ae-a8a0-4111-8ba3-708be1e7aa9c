# 智能手表增强UI界面说明

## 🎨 界面设计概述

智能手表现在具备了美化的用户界面和直观的按键控制系统，提供更好的用户体验。

## 🔘 按键功能

### 按键布局
```
STM32F103C8T6 开发板按键：
- KEY_UP (PB13)   : 查看传感器数据
- KEY_DOWN (PB14) : 返回时钟显示  
- KEY_MENU (PB15) : 切换显示模式
```

### 按键操作
| 按键 | 功能 | 说明 |
|------|------|------|
| **KEY_MENU** | 模式切换 | 在时钟和传感器显示间切换 |
| **KEY_UP** | 查看传感器 | 从时钟模式切换到传感器模式 |
| **KEY_DOWN** | 返回时钟 | 从传感器模式返回时钟模式 |

### 智能超时
- **自动返回**：在传感器模式下，10秒无操作自动返回时钟显示
- **防抖处理**：50ms按键防抖，避免误触发

## 📱 显示界面

### 🕐 时钟界面（默认显示）

```
┌─────────────────┐
│   15:30:45      │  ← 大字体时间显示
│  2025-07-27     │  ← 日期显示
│ Sun  MENU:Sensor│  ← 星期 + 操作提示
└─────────────────┘
```

**特色功能：**
- **居中对齐**：时间居中显示，视觉效果更佳
- **完整信息**：显示时、分、秒和完整日期
- **星期显示**：中英文星期显示（Mon/Tue/Wed...）
- **操作提示**：底部显示按键提示信息
- **状态指示**：右上角闪烁星号表示系统运行

### 📊 传感器界面（按键触发）

#### 完整传感器数据（有SGP30）
```
┌─────────────────┐
│ T:25.1C H:60.5% │  ← 温度和湿度
│ TVOC: 123 ppb   │  ← 总挥发性有机化合物
│ CO2:  456 ppm   │  ← 二氧化碳浓度
└─────────────────┘
```

#### 基础传感器数据（仅AHT10）
```
┌─────────────────┐
│   ENVIRONMENT   │  ← 标题
│  Temp: 25.1 C   │  ← 温度
│  Humi: 60.5 %   │  ← 湿度
└─────────────────┘
```

#### 传感器初始化中
```
┌─────────────────┐
│     SENSORS     │  ← 标题
│  Initializing   │  ← 状态提示
│   Please Wait   │  ← 等待信息
└─────────────────┘
```

## 🔄 交互流程

### 正常使用流程
```
开机启动
    ↓
时钟界面（默认）
    ↓
按 MENU/UP 键
    ↓
传感器界面
    ↓
10秒后自动返回 或 按DOWN键
    ↓
时钟界面
```

### 按键响应逻辑
```
时钟模式：
- MENU键 → 切换到传感器模式
- UP键   → 切换到传感器模式
- DOWN键 → 无效果（已在时钟模式）

传感器模式：
- MENU键 → 切换到时钟模式
- UP键   → 重置10秒超时
- DOWN键 → 立即返回时钟模式
```

## ⚙️ 技术实现

### 显示控制
```c
// 显示模式枚举
typedef enum {
    DISPLAY_MODE_CLOCK = 0,     // 时钟模式（默认）
    DISPLAY_MODE_SENSOR,        // 传感器模式
} display_mode_t;

// 全局控制变量
volatile display_mode_t g_display_mode = DISPLAY_MODE_CLOCK;
volatile uint32_t g_sensor_display_timeout = 0;
```

### 按键处理
- **扫描频率**：10ms扫描间隔
- **防抖时间**：50ms防抖处理
- **状态机**：边沿检测，按下和释放都检测

### 界面函数
```c
void Display_Clock_Interface(void);     // 时钟界面显示
void Display_Sensor_Interface(...);    // 传感器界面显示
```

## 🎯 用户体验特色

### 1. **直观操作**
- 默认显示时钟，符合手表使用习惯
- 按键查看传感器，按需显示
- 自动返回时钟，无需手动操作

### 2. **美观界面**
- 居中对齐的时间显示
- 清晰的信息层次
- 合理的空间布局

### 3. **智能交互**
- 10秒自动超时返回
- 按键防抖避免误操作
- 状态指示灯显示系统运行

### 4. **信息丰富**
- 完整的时间日期信息
- 星期显示
- 操作提示
- 多种传感器数据

## 🔧 自定义配置

### 修改超时时间
在 `freertos.c` 中修改：
```c
#define SENSOR_DISPLAY_TIMEOUT_MS 10000  // 改为其他值（毫秒）
```

### 修改防抖时间
在 `StartKeyTask` 中修改：
```c
#define KEY_DEBOUNCE_TIME 50  // 改为其他值（毫秒）
```

### 修改显示内容
- 修改 `Display_Clock_Interface()` 函数自定义时钟界面
- 修改 `Display_Sensor_Interface()` 函数自定义传感器界面

### 添加新的显示模式
1. 在 `display_mode_t` 枚举中添加新模式
2. 在按键处理中添加切换逻辑
3. 在显示任务中添加新的显示函数

## 📋 使用建议

### 日常使用
1. **查看时间**：正常情况下直接查看屏幕
2. **查看环境**：按MENU键查看温湿度等传感器数据
3. **快速返回**：按DOWN键立即返回时钟显示

### 功能扩展
1. **长按功能**：可以添加长按设置时间功能
2. **多级菜单**：可以扩展为多级菜单系统
3. **个性化**：可以添加主题切换功能

---

**更新时间**：2025-07-27  
**版本**：v2.0 Enhanced UI  
**状态**：✅ 完成并优化
