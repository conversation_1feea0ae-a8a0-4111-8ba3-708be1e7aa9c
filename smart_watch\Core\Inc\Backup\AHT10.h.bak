#ifndef __AHT10_H
#define __AHT10_H

#include <stdint.h>
#include "stm32f1xx_hal.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 全局变量声明 */
extern uint8_t aht10_calibrated;

/* AHT10 I2C地址 */
#define AHT10_ADDRESS_7BIT      0x38    // 7位地址（HAL库使用）
#define AHT10_ADDRESS_8BIT      0x70    // 8位写地址（参考代码格式）

/* AHT10 命令定义 */
#define AHT10_CMD_INIT          0xE1    // 初始化命令
#define AHT10_CMD_TRIGGER       0xAC    // 触发测量命令
#define AHT10_CMD_SOFT_RESET    0xBA    // 软复位命令

/* AHT10 状态位定义 */
#define AHT10_STATUS_BUSY       0x80    // 忙状态位
#define AHT10_STATUS_CAL        0x08    // 校准状态位

/* AHT10 数据长度 */
#define AHT10_DATA_LENGTH       6       // 数据长度（6字节）

/* AHT10 超时时间 */
#define AHT10_TIMEOUT           1000    // 超时时间（毫秒）

/* AHT10 测量延时 */
#define AHT10_MEASURE_DELAY     80      // 测量延时（毫秒）

/* 错误代码定义 */
typedef enum {
    AHT10_OK = 0,           // 成功
    AHT10_ERROR,            // 一般错误
    AHT10_TIMEOUT_ERROR,    // 超时错误
    AHT10_CRC_ERROR,        // CRC校验错误
    AHT10_NOT_CALIBRATED    // 未校准错误
} AHT10_Status_t;

/* 温湿度数据结构 */
typedef struct {
    float temperature;      // 温度值（摄氏度）
    float humidity;         // 湿度值（%RH）
} AHT10_Data_t;

/* 函数声明 */

/**
 * @brief AHT10初始化
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_Init(void);

/**
 * @brief AHT10软复位
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_SoftReset(void);

/**
 * @brief 读取AHT10状态
 * @param status 状态值指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadStatus(uint8_t *status);

/**
 * @brief 检查AHT10是否忙碌
 * @retval uint8_t 1-忙碌，0-空闲
 */
uint8_t AHT10_IsBusy(void);

/**
 * @brief 检查AHT10是否已校准
 * @retval uint8_t 1-已校准，0-未校准
 */
uint8_t AHT10_IsCalibrated(void);

/**
 * @brief 触发AHT10测量
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_TriggerMeasurement(void);

/**
 * @brief 读取AHT10原始数据
 * @param data 数据缓冲区（6字节）
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadRawData(uint8_t *data);

/**
 * @brief 读取AHT10温湿度数据
 * @param aht_data 温湿度数据结构指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_ReadData(AHT10_Data_t *aht_data);

/**
 * @brief 获取温湿度数据（一次完整的测量过程）
 * @param aht_data 温湿度数据结构指针
 * @retval AHT10_Status_t 状态码
 */
AHT10_Status_t AHT10_GetTemperatureHumidity(AHT10_Data_t *aht_data);

/**
 * @brief CRC8校验
 * @param data 数据指针
 * @param length 数据长度
 * @retval uint8_t CRC8值
 */
uint8_t AHT10_CRC8(uint8_t *data, uint8_t length);

/**
 * @brief 检测AHT10是否存在
 * @retval uint8_t 1-存在(0x38)，2-存在(0x39)，0-不存在
 */
uint8_t AHT10_IsPresent(void);

/**
 * @brief I2C总线扫描
 * @param found_devices 找到的设备地址数组
 * @param max_devices 最大设备数量
 * @retval uint8_t 找到的设备数量
 */
uint8_t AHT10_I2C_Scan(uint8_t *found_devices, uint8_t max_devices);

/**
 * @brief 简单测试函数
 * @retval uint8_t 测试结果
 */
uint8_t AHT10_SimpleTest(void);

#ifdef __cplusplus
}
#endif

#endif /* __AHT10_H */
