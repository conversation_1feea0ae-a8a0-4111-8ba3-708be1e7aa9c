智能手表界面演示
==================

🕐 时钟界面（默认显示）
┌─────────────────────────┐
│                         │
│      15:30:45      *    │  ← 时间 + 状态指示
│                         │
│     2025-07-27          │  ← 日期
│                         │
│   Sun  MENU:Sensor      │  ← 星期 + 提示
│                         │
└─────────────────────────┘

按键操作：MENU键 或 UP键 → 切换到传感器界面

📊 传感器界面（按键触发）
┌─────────────────────────┐
│                         │
│  T:25.1C H:60.5%        │  ← 温湿度
│                         │
│  TVOC: 123 ppb          │  ← 空气质量
│                         │
│  CO2:  456 ppm          │  ← 二氧化碳
│                         │
└─────────────────────────┘

自动返回：10秒后自动返回时钟界面
手动返回：DOWN键立即返回

🔘 按键功能说明
┌─────────────────────────┐
│  KEY_UP (PB13)          │
│  ├─ 查看传感器数据       │
│                         │
│  KEY_DOWN (PB14)        │
│  ├─ 返回时钟显示         │
│                         │
│  KEY_MENU (PB15)        │
│  ├─ 切换显示模式         │
└─────────────────────────┘

🔄 交互流程图
开机启动
    ↓
┌─────────────┐
│  时钟界面    │ ←─────────┐
│  (默认)     │           │
└─────────────┘           │
    ↓ MENU/UP             │ 10秒超时
┌─────────────┐           │ 或DOWN键
│  传感器界面  │ ──────────┘
│  (临时)     │
└─────────────┘

✨ 特色功能
• 美观的居中时间显示
• 完整的日期和星期信息
• 智能的按键控制
• 自动超时返回机制
• 防抖按键处理
• 状态指示灯
• 多种传感器数据显示
• 清晰的操作提示

🎯 用户体验
1. 默认显示时钟 - 符合手表使用习惯
2. 按需查看传感器 - 避免信息过载
3. 自动返回时钟 - 无需手动操作
4. 直观的按键操作 - 简单易用

📱 界面尺寸适配
• 针对128x64 OLED屏幕优化
• 8x16字体确保清晰显示
• 合理的信息布局
• 充分利用屏幕空间
