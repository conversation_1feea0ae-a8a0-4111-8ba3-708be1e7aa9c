# 多闹钟功能设计方案

## 📋 功能概述

将当前的单闹钟系统扩展为支持5个独立闹钟的多闹钟系统，每个闹钟可以独立设置时间、开关状态和重复模式。

## 🏗️ 系统架构

### 数据结构设计

```c
// 扩展的闹钟结构
typedef struct {
    uint8_t hour;           // 小时 (0-23)
    uint8_t minute;         // 分钟 (0-59)
    uint8_t enabled;        // 是否启用 (0/1)
    uint8_t triggered;      // 是否已触发 (0/1)
    uint8_t repeat_days;    // 重复日期位掩码 (bit0=周一, bit6=周日)
    char label[8];          // 闹钟标签 ("Alarm1", "Work", etc.)
} multi_alarm_t;

// 多闹钟管理结构
typedef struct {
    multi_alarm_t alarms[5];    // 5个闹钟
    uint8_t current_alarm;      // 当前选中的闹钟 (0-4)
    uint8_t alarm_count;        // 已设置的闹钟数量
} alarm_manager_t;
```

### 显示模式扩展

```c
// 在原有显示模式基础上添加
typedef enum {
    DISPLAY_MODE_CLOCK = 0,
    DISPLAY_MODE_SENSOR,
    DISPLAY_MODE_ALARM,         // 闹钟概览模式
    DISPLAY_MODE_ALARM_LIST,    // 闹钟列表模式 (新增)
    DISPLAY_MODE_TIMER,
    DISPLAY_MODE_STOPWATCH,
    DISPLAY_MODE_COUNT
} display_mode_t;

// 设置模式扩展
typedef enum {
    SETTING_MODE_NONE = 0,
    SETTING_MODE_TIME,
    SETTING_MODE_ALARM,         // 单个闹钟设置
    SETTING_MODE_ALARM_LIST,    // 闹钟列表管理 (新增)
    SETTING_MODE_TIMER
} setting_mode_t;
```

## 🎮 操作逻辑设计

### 界面层级结构

```
时钟模式 → MENU键 → 传感器模式 → MENU键 → 闹钟概览 → MENU键 → 闹钟列表 → ...
    ↑                                           ↓                    ↓
    └─────────── DOWN键返回 ←─────────────────────┘                    ↓
                                                                     ↓
                                                              SET键进入设置
                                                                     ↓
                                                              单个闹钟设置界面
```

### 按键功能映射

#### 闹钟概览模式 (DISPLAY_MODE_ALARM)
- **UP键**: 快速开关当前主闹钟
- **DOWN键**: 返回时钟模式
- **MENU键**: 进入闹钟列表模式
- **SET键**: 设置当前主闹钟

#### 闹钟列表模式 (DISPLAY_MODE_ALARM_LIST)
- **UP键**: 上一个闹钟
- **DOWN键**: 下一个闹钟
- **MENU键**: 返回闹钟概览模式
- **SET键**: 设置当前选中的闹钟

#### 闹钟设置模式 (SETTING_MODE_ALARM)
- **UP键**: 增加当前设置项数值
- **DOWN键**: 减少当前设置项数值
- **MENU键**: 保存并退出设置
- **SET键**: 切换到下一个设置项

## 📱 界面设计

### 1. 闹钟概览界面
```
┌─────────────────────────┐
│       ALARMS            │
│                         │
│  ⏰ 06:30  ON   Work    │  ← 主闹钟
│  📅 Mon-Fri             │  ← 重复设置
│                         │
│  Next: 07:00 Study      │  ← 下一个闹钟
│                         │
│ MENU:List SET:Edit      │  ← 操作提示
└─────────────────────────┘
```

### 2. 闹钟列表界面
```
┌─────────────────────────┐
│     ALARM LIST          │
│                         │
│ ► 1. 06:30  ON  Work    │  ← 当前选中
│   2. 07:00  OFF Study   │
│   3. 12:00  ON  Lunch   │
│   4. --:--  OFF         │
│   5. --:--  OFF         │
│                         │
│ UP/DOWN:Select SET:Edit │
└─────────────────────────┘
```

### 3. 闹钟设置界面
```
┌─────────────────────────┐
│    SET ALARM 1          │
│                         │
│ Time:  [06]:[30]        │  ← 时间设置
│ State: [ON]             │  ← 开关设置
│ Repeat:[Mon-Fri]        │  ← 重复设置
│ Label: [Work    ]       │  ← 标签设置
│                         │
│ SET:Next UP:+ DOWN:-    │
└─────────────────────────┘
```

### 4. 重复设置界面
```
┌─────────────────────────┐
│   REPEAT SETTINGS       │
│                         │
│ Mon [✓] Tue [✓] Wed [✓] │
│ Thu [✓] Fri [✓] Sat [ ] │
│ Sun [ ]                 │
│                         │
│ Preset: [Weekdays]      │  ← 快速设置
│                         │
│ UP/DOWN:Toggle SET:Next │
└─────────────────────────┘
```

## 🔧 核心功能实现

### 1. 闹钟检查逻辑
```c
void Check_Multi_Alarms(void)
{
    for (int i = 0; i < 5; i++) {
        multi_alarm_t *alarm = &g_alarm_manager.alarms[i];
        
        if (!alarm->enabled || alarm->triggered) {
            continue;
        }
        
        // 检查时间匹配
        if (g_current_time.hour == alarm->hour && 
            g_current_time.minute == alarm->minute) {
            
            // 检查重复日期
            if (Check_Alarm_Repeat(alarm, g_current_time.day)) {
                alarm->triggered = 1;
                Buzzer_Start_Alarm_With_ID(i);  // 带ID的响铃
            }
        }
        
        // 重置触发状态
        if (alarm->triggered && g_current_time.minute != alarm->minute) {
            alarm->triggered = 0;
        }
    }
}
```

### 2. 重复日期检查
```c
uint8_t Check_Alarm_Repeat(multi_alarm_t *alarm, uint8_t current_day)
{
    // 如果没有设置重复，只响一次
    if (alarm->repeat_days == 0) {
        return 1;
    }
    
    // 检查当前星期是否在重复设置中
    // current_day: 1=周一, 7=周日
    uint8_t day_bit = 1 << (current_day - 1);
    return (alarm->repeat_days & day_bit) != 0;
}
```

### 3. 闹钟管理功能
```c
// 添加新闹钟
uint8_t Add_New_Alarm(uint8_t hour, uint8_t minute, const char* label)
{
    for (int i = 0; i < 5; i++) {
        if (!g_alarm_manager.alarms[i].enabled) {
            g_alarm_manager.alarms[i].hour = hour;
            g_alarm_manager.alarms[i].minute = minute;
            g_alarm_manager.alarms[i].enabled = 1;
            g_alarm_manager.alarms[i].triggered = 0;
            g_alarm_manager.alarms[i].repeat_days = 0;
            strncpy(g_alarm_manager.alarms[i].label, label, 7);
            g_alarm_manager.alarms[i].label[7] = '\0';
            return i;  // 返回闹钟ID
        }
    }
    return 0xFF;  // 闹钟已满
}

// 删除闹钟
void Delete_Alarm(uint8_t alarm_id)
{
    if (alarm_id < 5) {
        memset(&g_alarm_manager.alarms[alarm_id], 0, sizeof(multi_alarm_t));
    }
}

// 获取下一个闹钟
uint8_t Get_Next_Alarm(void)
{
    uint32_t current_minutes = g_current_time.hour * 60 + g_current_time.minute;
    uint32_t next_minutes = 24 * 60;  // 明天的0点
    uint8_t next_alarm_id = 0xFF;
    
    for (int i = 0; i < 5; i++) {
        if (!g_alarm_manager.alarms[i].enabled) continue;
        
        uint32_t alarm_minutes = g_alarm_manager.alarms[i].hour * 60 + 
                                g_alarm_manager.alarms[i].minute;
        
        if (alarm_minutes > current_minutes && alarm_minutes < next_minutes) {
            next_minutes = alarm_minutes;
            next_alarm_id = i;
        }
    }
    
    return next_alarm_id;
}
```

## 🎯 重复模式设计

### 重复模式位掩码
```c
#define REPEAT_MONDAY    (1 << 0)  // 0x01
#define REPEAT_TUESDAY   (1 << 1)  // 0x02
#define REPEAT_WEDNESDAY (1 << 2)  // 0x04
#define REPEAT_THURSDAY  (1 << 3)  // 0x08
#define REPEAT_FRIDAY    (1 << 4)  // 0x10
#define REPEAT_SATURDAY  (1 << 5)  // 0x20
#define REPEAT_SUNDAY    (1 << 6)  // 0x40

// 预设模式
#define REPEAT_WEEKDAYS  (REPEAT_MONDAY | REPEAT_TUESDAY | REPEAT_WEDNESDAY | REPEAT_THURSDAY | REPEAT_FRIDAY)
#define REPEAT_WEEKEND   (REPEAT_SATURDAY | REPEAT_SUNDAY)
#define REPEAT_EVERYDAY  (0x7F)
#define REPEAT_ONCE      (0x00)
```

### 重复模式显示
```c
void Display_Repeat_Pattern(uint8_t repeat_days, char* buffer)
{
    if (repeat_days == 0) {
        strcpy(buffer, "Once");
    } else if (repeat_days == REPEAT_WEEKDAYS) {
        strcpy(buffer, "Mon-Fri");
    } else if (repeat_days == REPEAT_WEEKEND) {
        strcpy(buffer, "Weekend");
    } else if (repeat_days == REPEAT_EVERYDAY) {
        strcpy(buffer, "Everyday");
    } else {
        // 自定义模式，显示具体日期
        buffer[0] = '\0';
        if (repeat_days & REPEAT_MONDAY) strcat(buffer, "M");
        if (repeat_days & REPEAT_TUESDAY) strcat(buffer, "T");
        if (repeat_days & REPEAT_WEDNESDAY) strcat(buffer, "W");
        if (repeat_days & REPEAT_THURSDAY) strcat(buffer, "T");
        if (repeat_days & REPEAT_FRIDAY) strcat(buffer, "F");
        if (repeat_days & REPEAT_SATURDAY) strcat(buffer, "S");
        if (repeat_days & REPEAT_SUNDAY) strcat(buffer, "S");
    }
}
```

## 📊 内存使用分析

### 内存占用计算
```c
// 单个闹钟结构大小
sizeof(multi_alarm_t) = 1+1+1+1+1+8 = 13 bytes

// 5个闹钟总大小
5 * 13 = 65 bytes

// 管理结构
sizeof(alarm_manager_t) = 65 + 1 + 1 = 67 bytes

// 临时变量
sizeof(multi_alarm_t) = 13 bytes (设置时使用)

// 总计约 80 bytes
```

### Flash存储需求
- 闹钟数据持久化存储
- 使用内部Flash的最后几页
- 实现磨损均衡算法

## 🚀 实现优先级

### 第一阶段：基础多闹钟 (2-3天)
1. 扩展数据结构
2. 实现闹钟列表界面
3. 基础的多闹钟检查逻辑

### 第二阶段：重复功能 (1-2天)
1. 重复日期设置
2. 重复模式检查
3. 预设重复模式

### 第三阶段：高级功能 (1-2天)
1. 闹钟标签设置
2. 闹钟排序显示
3. 数据持久化存储

## 🎯 用户体验优化

### 智能默认设置
- 新闹钟默认标签："Alarm1", "Alarm2"...
- 智能时间建议（避免重复时间）
- 常用重复模式快速选择

### 视觉反馈
- 启用的闹钟用不同图标显示
- 下一个闹钟高亮显示
- 重复模式用简洁符号表示

### 操作便捷性
- 长按快速开关闹钟
- 双击快速编辑
- 智能排序（按时间顺序）

这个设计方案将为您的智能手表带来专业级的多闹钟功能！您觉得这个方案如何？我可以开始实现具体的代码。
