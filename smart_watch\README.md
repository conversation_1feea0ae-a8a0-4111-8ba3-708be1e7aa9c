# Smart Watch Project

## 项目概述
这是一个基于STM32F103C8T6的智能手表项目，具有温湿度监测和OLED显示功能。

## 硬件配置

### 主控制器
- STM32F103C8T6

### 外设连接
- **AHT10温湿度传感器** - 连接到I2C1
  - SCL: PB6
  - SDA: PB7
  - 地址: 0x38

- **OLED 0.96寸显示屏** - 连接到I2C2
  - SCL: PB10
  - SDA: PB11
  - 地址: 0x78

- **按键**
  - KEY_UP: PB13
  - KEY_DOWN: PB14
  - KEY_MENU: PB15

## 软件架构

### FreeRTOS任务
1. **DefaultTask** - 系统默认任务，负责系统监控
2. **SensorTask** - 传感器任务，负责读取AHT10温湿度数据
3. **DisplayTask** - 显示任务，负责OLED显示控制
4. **KeyTask** - 按键任务，负责按键处理（待实现）

### 队列和互斥锁
- **SensorDataQueue** - 传感器数据队列，用于传感器任务向显示任务传递数据
- **KeyEventQueue** - 按键事件队列，用于按键事件处理
- **DisplayMutex** - 显示互斥锁，保护OLED显示资源
- **TimeUpdateSem** - 时间更新信号量

## 功能特性

### 温湿度监测
- 使用AHT10传感器读取环境温湿度
- 每5秒更新一次数据
- 温度精度：±0.3°C
- 湿度精度：±2%RH

### OLED显示
- 0.96寸OLED显示屏
- 显示内容：
  - 设备名称："Smart Watch"
  - 实时温度值
  - 实时湿度值
- 每1秒刷新一次显示

## 编译和烧录

### 开发环境
- STM32CubeIDE 或 Keil MDK
- STM32CubeMX（用于配置生成）

### 编译步骤
1. 打开项目文件
2. 编译项目
3. 生成hex文件

### 烧录步骤
1. 连接ST-Link调试器
2. 使用STM32CubeProgrammer或IDE内置烧录工具
3. 烧录hex文件到目标板

## 使用说明

### 上电启动
1. 连接电源
2. 系统自动初始化
3. OLED显示"Smart Watch"和"Starting..."
4. 等待传感器初始化完成
5. 开始显示温湿度数据

### 显示界面
```
Smart Watch
Temp: 25.3 C
Humi: 65.2 %
```

## 故障排除

### 常见问题
1. **OLED无显示**
   - 检查I2C2连接（PB10, PB11）
   - 检查OLED电源连接
   - 检查I2C地址是否正确（0x78）

2. **温湿度数据异常**
   - 检查I2C1连接（PB6, PB7）
   - 检查AHT10电源连接
   - 检查传感器是否正确初始化

3. **系统无响应**
   - 检查电源供电
   - 检查晶振连接
   - 重新烧录固件

## 扩展功能
- [ ] 按键功能实现
- [ ] 时间显示功能
- [ ] 数据记录功能
- [ ] 低功耗模式
- [ ] 蓝牙通信功能

## 版本历史
- V1.0 - 基础温湿度监测和显示功能

## 作者
Smart Watch Project Team
