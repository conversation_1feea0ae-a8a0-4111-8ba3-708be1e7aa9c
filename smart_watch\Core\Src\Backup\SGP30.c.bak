#include "SGP30.h"
#include "i2c.h"
#include <string.h>

/* 全局变量 */
uint8_t sgp30_initialized = 0;

/**
 * @brief SGP30初始化
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_Init(void)
{
    HAL_StatusTypeDef hal_status;
    uint8_t init_cmd[2] = {(SGP30_CMD_INIT_AIR_QUALITY >> 8), (SGP30_CMD_INIT_AIR_QUALITY & 0xFF)};
    
    // 等待传感器上电稳定
    HAL_Delay(100);
    
    // 发送初始化命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, SGP30_ADDRESS_7BIT << 1, init_cmd, 2, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    // 等待初始化完成（15秒）
    HAL_Delay(SGP30_INIT_DELAY);
    
    sgp30_initialized = 1;
    return SGP30_OK;
}

/**
 * @brief 检测SGP30是否存在
 * @retval uint8_t 1-存在，0-不存在
 */
uint8_t SGP30_IsPresent(void)
{
    HAL_StatusTypeDef hal_status;
    
    // 尝试向SGP30发送命令来检测是否存在
    hal_status = HAL_I2C_IsDeviceReady(&hi2c1, SGP30_ADDRESS_7BIT << 1, 3, 100);
    
    return (hal_status == HAL_OK) ? 1 : 0;
}

/**
 * @brief CRC8校验
 * @param data 数据指针
 * @param length 数据长度
 * @retval uint8_t CRC8值
 */
uint8_t SGP30_CRC8(uint8_t *data, uint8_t length)
{
    uint8_t crc = 0xFF;
    uint8_t i, j;
    
    for (i = 0; i < length; i++) {
        crc ^= data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x31;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 获取空气质量数据
 * @param sgp_data 空气质量数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_GetAirQuality(SGP30_Data_t *sgp_data)
{
    HAL_StatusTypeDef hal_status;
    uint8_t measure_cmd[2] = {(SGP30_CMD_MEASURE_AIR_QUALITY >> 8), (SGP30_CMD_MEASURE_AIR_QUALITY & 0xFF)};
    uint8_t raw_data[6];
    uint8_t calculated_crc;
    
    if (sgp_data == NULL) {
        return SGP30_ERROR;
    }
    
    if (!sgp30_initialized) {
        return SGP30_NOT_INITIALIZED;
    }
    
    // 发送测量命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, SGP30_ADDRESS_7BIT << 1, measure_cmd, 2, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    // 等待测量完成
    HAL_Delay(SGP30_MEASURE_DELAY);
    
    // 读取数据
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (SGP30_ADDRESS_7BIT << 1) | 1, raw_data, 6, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    // 验证CRC（每2字节数据后跟1字节CRC）
    calculated_crc = SGP30_CRC8(&raw_data[0], 2);
    if (calculated_crc != raw_data[2]) {
        return SGP30_CRC_ERROR;
    }
    
    calculated_crc = SGP30_CRC8(&raw_data[3], 2);
    if (calculated_crc != raw_data[5]) {
        return SGP30_CRC_ERROR;
    }
    
    // 解析数据
    sgp_data->eco2_ppm = (raw_data[0] << 8) | raw_data[1];
    sgp_data->tvoc_ppb = (raw_data[3] << 8) | raw_data[4];
    sgp_data->data_valid = 1;
    
    return SGP30_OK;
}

/**
 * @brief 设置湿度补偿
 * @param humidity 湿度值 (0-100%)
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_SetHumidity(float humidity)
{
    HAL_StatusTypeDef hal_status;
    uint8_t cmd_data[5];
    uint16_t humidity_raw;
    uint8_t crc;
    
    if (!sgp30_initialized) {
        return SGP30_NOT_INITIALIZED;
    }
    
    // 转换湿度值为SGP30格式 (humidity * 256 / 100)
    humidity_raw = (uint16_t)(humidity * 256.0f / 100.0f);
    
    // 构建命令
    cmd_data[0] = (SGP30_CMD_SET_HUMIDITY >> 8);
    cmd_data[1] = (SGP30_CMD_SET_HUMIDITY & 0xFF);
    cmd_data[2] = (humidity_raw >> 8);
    cmd_data[3] = (humidity_raw & 0xFF);
    
    // 计算CRC
    crc = SGP30_CRC8(&cmd_data[2], 2);
    cmd_data[4] = crc;
    
    // 发送命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, SGP30_ADDRESS_7BIT << 1, cmd_data, 5, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    return SGP30_OK;
}

/**
 * @brief 获取基线值
 * @param baseline 基线数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_GetBaseline(SGP30_Baseline_t *baseline)
{
    HAL_StatusTypeDef hal_status;
    uint8_t get_cmd[2] = {(SGP30_CMD_GET_BASELINE >> 8), (SGP30_CMD_GET_BASELINE & 0xFF)};
    uint8_t raw_data[6];
    uint8_t calculated_crc;
    
    if (baseline == NULL) {
        return SGP30_ERROR;
    }
    
    if (!sgp30_initialized) {
        return SGP30_NOT_INITIALIZED;
    }
    
    // 发送获取基线命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, SGP30_ADDRESS_7BIT << 1, get_cmd, 2, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    // 等待响应
    HAL_Delay(10);
    
    // 读取基线数据
    hal_status = HAL_I2C_Master_Receive(&hi2c1, (SGP30_ADDRESS_7BIT << 1) | 1, raw_data, 6, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    // 验证CRC
    calculated_crc = SGP30_CRC8(&raw_data[0], 2);
    if (calculated_crc != raw_data[2]) {
        return SGP30_CRC_ERROR;
    }
    
    calculated_crc = SGP30_CRC8(&raw_data[3], 2);
    if (calculated_crc != raw_data[5]) {
        return SGP30_CRC_ERROR;
    }
    
    // 解析基线数据
    baseline->eco2_baseline = (raw_data[0] << 8) | raw_data[1];
    baseline->tvoc_baseline = (raw_data[3] << 8) | raw_data[4];
    
    return SGP30_OK;
}

/**
 * @brief 设置基线值
 * @param baseline 基线数据结构指针
 * @retval SGP30_Status_t 状态码
 */
SGP30_Status_t SGP30_SetBaseline(SGP30_Baseline_t *baseline)
{
    HAL_StatusTypeDef hal_status;
    uint8_t cmd_data[8];
    uint8_t crc;
    
    if (baseline == NULL) {
        return SGP30_ERROR;
    }
    
    if (!sgp30_initialized) {
        return SGP30_NOT_INITIALIZED;
    }
    
    // 构建命令
    cmd_data[0] = (SGP30_CMD_SET_BASELINE >> 8);
    cmd_data[1] = (SGP30_CMD_SET_BASELINE & 0xFF);
    
    // TVOC基线
    cmd_data[2] = (baseline->tvoc_baseline >> 8);
    cmd_data[3] = (baseline->tvoc_baseline & 0xFF);
    crc = SGP30_CRC8(&cmd_data[2], 2);
    cmd_data[4] = crc;
    
    // eCO2基线
    cmd_data[5] = (baseline->eco2_baseline >> 8);
    cmd_data[6] = (baseline->eco2_baseline & 0xFF);
    crc = SGP30_CRC8(&cmd_data[5], 2);
    cmd_data[7] = crc;
    
    // 发送命令
    hal_status = HAL_I2C_Master_Transmit(&hi2c1, SGP30_ADDRESS_7BIT << 1, cmd_data, 8, SGP30_TIMEOUT);
    if (hal_status != HAL_OK) {
        return SGP30_ERROR;
    }
    
    return SGP30_OK;
}
