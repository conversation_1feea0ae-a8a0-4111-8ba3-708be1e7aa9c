/**
  ******************************************************************************
  * @file    json_parser.h
  * @brief   轻量级JSON解析器头文件 - 专用于天气数据解析
  * <AUTHOR> Watch Team
  * @date    2025-07-31
  ******************************************************************************
  */

#ifndef __JSON_PARSER_H
#define __JSON_PARSER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief JSON解析结果枚举
 */
typedef enum {
    JSON_PARSE_OK = 0,              // 解析成功
    JSON_PARSE_ERROR,               // 解析错误
    JSON_PARSE_NOT_FOUND,           // 字段未找到
    JSON_PARSE_INVALID_FORMAT       // 格式无效
} json_parse_result_t;

/* Exported constants --------------------------------------------------------*/

// 解析缓冲区大小
#define JSON_MAX_STRING_LEN         64
#define JSON_MAX_NUMBER_LEN         16

/* Exported macro ------------------------------------------------------------*/

// 调试输出宏
#ifdef JSON_DEBUG
#define JSON_LOG(fmt, ...) printf("[JSON] " fmt "\r\n", ##__VA_ARGS__)
#else
#define JSON_LOG(fmt, ...)
#endif

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 从JSON字符串中提取字符串值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetString(const char* json, const char* key, char* value, size_t value_size);

/**
 * @brief 从JSON字符串中提取整数值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出整数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetInt(const char* json, const char* key, int* value);

/**
 * @brief 从JSON字符串中提取浮点数值
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @param value: 输出浮点数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetFloat(const char* json, const char* key, float* value);

/**
 * @brief 从嵌套JSON对象中提取字符串值
 * @param json: JSON字符串
 * @param parent_key: 父对象键名
 * @param child_key: 子对象键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetNestedString(const char* json, const char* parent_key, 
                                        const char* child_key, char* value, size_t value_size);

/**
 * @brief 从嵌套JSON对象中提取浮点数值
 * @param json: JSON字符串
 * @param parent_key: 父对象键名
 * @param child_key: 子对象键名
 * @param value: 输出浮点数指针
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetNestedFloat(const char* json, const char* parent_key, 
                                       const char* child_key, float* value);

/**
 * @brief 从JSON数组中提取第一个对象的字符串值
 * @param json: JSON字符串
 * @param array_key: 数组键名
 * @param object_key: 对象键名
 * @param value: 输出缓冲区
 * @param value_size: 缓冲区大小
 * @retval JSON解析结果
 */
json_parse_result_t JSON_GetArrayFirstString(const char* json, const char* array_key, 
                                            const char* object_key, char* value, size_t value_size);

/**
 * @brief 查找JSON对象的开始位置
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @retval 对象开始位置指针，NULL表示未找到
 */
const char* JSON_FindObject(const char* json, const char* key);

/**
 * @brief 查找JSON数组的开始位置
 * @param json: JSON字符串
 * @param key: 要查找的键名
 * @retval 数组开始位置指针，NULL表示未找到
 */
const char* JSON_FindArray(const char* json, const char* key);

/**
 * @brief 跳过JSON值（用于解析时跳过不需要的值）
 * @param json: 当前位置指针
 * @retval 跳过值后的位置指针
 */
const char* JSON_SkipValue(const char* json);

/**
 * @brief 去除字符串两端的引号和空白字符
 * @param str: 要处理的字符串
 * @param output: 输出缓冲区
 * @param output_size: 缓冲区大小
 * @retval 0: 成功, -1: 失败
 */
int JSON_TrimQuotes(const char* str, char* output, size_t output_size);

#ifdef __cplusplus
}
#endif

#endif /* __JSON_PARSER_H */
