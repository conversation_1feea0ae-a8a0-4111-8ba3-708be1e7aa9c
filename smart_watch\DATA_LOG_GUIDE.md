# 📊 数据记录和统计功能使用指南

## 🎯 功能概述

您的智能手表现在具备了环境数据记录和统计功能！专门为0.96寸OLED屏幕优化设计。

### 核心功能
- **自动记录**：每10分钟自动记录一次环境数据
- **24小时数据**：可存储144个数据点（24小时×6次/小时）
- **实时统计**：自动计算今日最高/最低值
- **趋势显示**：简单的ASCII趋势图
- **内存优化**：仅占用约1.5KB RAM

## 🎮 操作方法

### 进入数据记录模式
1. **模式切换**：按MENU键循环切换到数据记录模式
2. **模式顺序**：时钟 → 传感器 → 闹钟 → **数据记录** → 计时器 → 秒表

### 查看不同数据视图
在数据记录模式下：
- **UP键**：切换到上一个视图
- **DOWN键**：切换到下一个视图
- **MENU键**：进入下一个模式（计时器）

## 📱 三种数据视图

### 1. 数据概览（默认视图）
```
┌─────────────────────┐
│   DATA LOG          │
│                     │
│ Now:25°C 65%        │  ← 当前实时数据
│ Max:28°C 78%        │  ← 今日最高值
│ Min:22°C 45%        │  ← 今日最低值
└─────────────────────┘
```

**显示内容**：
- **Now**: 当前温度和湿度
- **Max**: 今日最高温度和湿度
- **Min**: 今日最低温度和湿度

### 2. 温度趋势（趋势视图）
```
┌─────────────────────┐
│  TEMP TREND         │
│                     │
│ ^^--__--^^--        │  ← ASCII趋势图
│ Range:22-28°C       │  ← 温度范围
│ ^High -Mid _Low     │  ← 图例说明
└─────────────────────┘
```

**显示内容**：
- **趋势图**: 最近12个数据点的温度趋势
- **符号含义**:
  - `^` = 高温（接近最高值）
  - `-` = 中等温度
  - `_` = 低温（接近最低值）
- **Range**: 今日温度范围

### 3. 详细统计（统计视图）
```
┌─────────────────────┐
│  STATISTICS         │
│                     │
│ Points:156/144      │  ← 数据点数量
│ Peak:14:00(28°C)    │  ← 最高温度时间
│ Low: 06:00(22°C)    │  ← 最低温度时间
└─────────────────────┘
```

**显示内容**：
- **Points**: 已记录的数据点数量/总容量
- **Peak**: 最高温度出现的时间和数值
- **Low**: 最低温度出现的时间和数值

## 🔧 技术特性

### 数据记录规格
- **记录频率**：每10分钟一次
- **存储容量**：144个数据点（24小时）
- **数据类型**：温度、湿度、CO2浓度
- **精度**：温度±1°C，湿度±1%
- **存储方式**：循环缓冲区（自动覆盖旧数据）

### 内存使用
```c
单个数据点：10 bytes
144个数据点：1440 bytes
管理结构：约100 bytes
总计：约1.5KB RAM
```

### 数据结构
```c
typedef struct {
    uint8_t hour;           // 小时
    uint8_t minute;         // 分钟
    int8_t temperature;     // 温度（整数）
    uint8_t humidity;       // 湿度（整数）
    uint16_t co2;           // CO2浓度
} data_point_t;
```

## 📊 数据分析功能

### 自动统计
系统自动计算并更新：
- **温度统计**：最高/最低温度及出现时间
- **湿度统计**：最高/最低湿度
- **CO2统计**：最高/最低CO2浓度
- **数据计数**：已记录的数据点数量

### 趋势分析
- **实时趋势**：显示最近2小时的温度变化
- **视觉化**：用ASCII字符直观显示趋势
- **范围显示**：显示当日温度变化范围

## 🕐 使用场景

### 家庭环境监测
- **室内温湿度**：了解家中环境变化规律
- **空调优化**：根据数据调整空调使用
- **健康管理**：监测适宜的居住环境

### 办公环境分析
- **工作环境**：监测办公室环境质量
- **通风提醒**：根据CO2浓度判断通风需求
- **舒适度评估**：分析最舒适的环境条件

### 数据收集爱好
- **长期记录**：收集环境数据进行分析
- **季节变化**：观察不同季节的环境特点
- **模式发现**：发现环境变化的规律

## 🔄 数据管理

### 自动管理
- **循环存储**：新数据自动覆盖最旧数据
- **无需手动清理**：系统自动管理存储空间
- **连续记录**：24小时不间断记录

### 数据重置
- **每日重置**：统计数据在新的一天自动重置
- **手动重置**：重启设备会清空所有记录
- **数据保护**：正常使用不会丢失数据

## 🧪 使用技巧

### 1. 最佳观察时间
- **早上查看**：了解夜间环境变化
- **下午查看**：观察白天温度峰值
- **晚上查看**：分析全天环境趋势

### 2. 数据解读
- **温度趋势**：
  - `^^^^` = 持续升温
  - `____` = 持续降温
  - `--` = 温度稳定
- **异常检测**：突然的温湿度变化可能表示环境异常

### 3. 实用建议
- **定期查看**：每天查看1-2次了解环境状况
- **对比分析**：比较不同时间段的数据
- **环境调节**：根据数据调整室内环境

## 📋 常见问题

### Q: 为什么没有数据显示？
A: 需要等待10分钟后才会有第一个数据点记录。

### Q: 数据点数量为什么会减少？
A: 当存储满144个点后，新数据会覆盖最旧的数据。

### Q: 趋势图看不懂怎么办？
A: 
- `^` = 温度较高
- `-` = 温度中等  
- `_` = 温度较低

### Q: 统计数据什么时候重置？
A: 目前需要重启设备才会重置，后续可以添加每日自动重置功能。

## 🚀 未来扩展

### 可能的增强功能
1. **数据导出**：通过串口导出CSV格式数据
2. **更多图表**：湿度趋势、CO2趋势图
3. **异常警告**：温湿度超出正常范围时提醒
4. **数据持久化**：保存到Flash，断电不丢失
5. **每日重置**：每天0点自动重置统计数据

### 硬件扩展
1. **SD卡存储**：长期数据存储
2. **WiFi模块**：数据上传到云端
3. **更多传感器**：气压、光照等

## 🎊 总结

现在您的智能手表具备了专业的环境数据记录功能：

✅ **自动记录** - 每10分钟自动记录环境数据
✅ **实时统计** - 自动计算最高/最低值
✅ **趋势显示** - 直观的ASCII趋势图
✅ **小屏优化** - 专为0.96寸屏幕设计
✅ **内存高效** - 仅占用1.5KB内存
✅ **操作简单** - UP/DOWN键切换视图

您的智能手表现在不仅是时间工具，更是一个专业的环境监测设备！

**立即开始使用数据记录功能，了解您周围的环境变化吧！** 📊
